import React from 'react'

export default function PricingPlans() {
  	const plans = [
		{
			name: "基础版",
			subtitle: "适合小商家的基础解决方案",
			price: "22",
			originalPrice: "28.00",
			period: "USD/月",
			commission: "2%",
			features: ["产品数量: 6", "员工账户数量", "免费SSL证书", "移动端优化", "SEO优化工具", "24/7 在线客服"],
			buttonText: "立即开始",
			buttonClass: "bg-red-100 text-red-600 hover:bg-red-200",
		},
		{
			name: "进阶版",
			subtitle: "适合中型商家的进阶解决方案",
			price: "79",
			originalPrice: "99.00",
			period: "USD/月",
			commission: "0.6%",
			features: [
				"产品数量: 15",
				"员工账户数量",
				"免费SSL证书",
				"移动端优化",
				"SEO优化工具",
				"社交媒体整合",
				"24/7 在线客服",
			],
			buttonText: "立即开始",
			buttonClass: "bg-red-100 text-red-600 hover:bg-red-200",
		},
		{
			name: "PRO版",
			subtitle: "适合大型商家的专业解决方案",
			price: "174",
			originalPrice: "218.00",
			period: "USD/月",
			commission: "0.2%",
			popular: true,
			features: [
				"产品数量: 100",
				"员工账户数量",
				"免费SSL证书",
				"移动端优化",
				"SEO优化工具",
				"社交媒体整合",
				"B2B 批发功能",
				"Avalara 税务集成",
				"24/7 专属客服",
			],
			buttonText: "立即开始",
			buttonClass: "bg-red-600 text-white hover:bg-red-700",
		},
	];

    const features = [
    {
      category: "产品管理",
      items: [
        { name: "产品数量限制", basic: "6个", advanced: "15个", pro: "100个" },
        { name: "产品变体管理", basic: true, advanced: true, pro: true },
        { name: "批量导入导出", basic: false, advanced: true, pro: true },
        { name: "SEO优化设置", basic: true, advanced: true, pro: true }
      ]
    },
    {
      category: "网站功能",
      items: [
        { name: "免费SSL证书", basic: true, advanced: true, pro: true },
        { name: "移动端自适应", basic: true, advanced: true, pro: true },
        { name: "多语言支持", basic: false, advanced: true, pro: true },
        { name: "自定义域名", basic: true, advanced: true, pro: true }
      ]
    },
    {
      category: "营销推广",
      items: [
        { name: "SEO工具", basic: true, advanced: true, pro: true },
        { name: "社交媒体集成", basic: false, advanced: true, pro: true },
        { name: "邮件营销", basic: false, advanced: true, pro: true },
        { name: "优惠券系统", basic: true, advanced: true, pro: true }
      ]
    },
    {
      category: "订单管理",
      items: [
        { name: "订单处理", basic: true, advanced: true, pro: true },
        { name: "库存管理", basic: true, advanced: true, pro: true },
        { name: "发货管理", basic: true, advanced: true, pro: true },
        { name: "退换货管理", basic: false, advanced: true, pro: true }
      ]
    },
    {
      category: "支付配送",
      items: [
        { name: "支付网关集成", basic: true, advanced: true, pro: true },
        { name: "多种配送方式", basic: true, advanced: true, pro: true },
        { name: "税务计算", basic: false, advanced: false, pro: true },
        { name: "B2B批发功能", basic: false, advanced: false, pro: true }
      ]
    },
    {
      category: "客户服务",
      items: [
        { name: "在线客服", basic: "工作时间", advanced: "24/7", pro: "24/7专属" },
        { name: "技术支持", basic: "邮件", advanced: "邮件+电话", pro: "专属客服" },
        { name: "培训资源", basic: true, advanced: true, pro: true }
      ]
    }
  ];

    const renderValue = (value: boolean | string) => {
    if (typeof value === 'boolean') {
      return value ? (
        <svg className="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
      ) : (
        <svg className="w-5 h-5 text-gray-300 mx-auto" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      )
    }
    return <span className="text-sm text-center">{value}</span>
  }
  return (
    <>
      			<section className="py-[120px]">
				<div className="container px-4 sm:px-6 lg:px-8">
					{/* Header */}
					<div className="mb-12 text-center">
						<h1 className="mb-4 text-4xl font-bold text-gray-900">套餐及定价</h1>
						<p className="mb-8 text-lg text-gray-600">灵活的定价策略，满足不同规模商家的需求</p>

						{/* Billing Toggle */}
						<div className="mb-8 flex items-center justify-center space-x-4">
							<button className="rounded border bg-white px-4 py-2 text-sm">包月付费</button>
							<button className="rounded border bg-white px-4 py-2 text-sm">包年付费</button>
							<div className="rounded-full bg-red-100 px-3 py-1 text-sm text-main">年付享8折</div>
						</div>

						{/* Commission Info */}
						<div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
							<span>佣金 (包含支付)</span>
							<span>另计</span>
						</div>
					</div>

					{/* Pricing Cards */}
					<div className="mx-auto grid container gap-8 md:grid-cols-3">
						{plans.map((plan, index) => (
							<div
								key={index}
								className={`relative rounded-lg bg-white p-8 shadow-lg ${
									plan.popular ? "ring-2 ring-red-600" : ""
								}`}
							>
								{plan.popular && (
									<div className="absolute right-4 top-0 rounded-b bg-red-600 px-3 py-1 text-sm text-white">
										推荐
									</div>
								)}

								<div className="mb-6 text-center">
									<h3 className="mb-2 text-xl font-bold text-gray-900">{plan.name}</h3>
									<p className="mb-4 text-sm text-gray-600">{plan.subtitle}</p>

									<div className="mb-4">
										<span className="text-3xl font-bold text-gray-900">${plan.price}</span>
										<span className="ml-2 text-lg text-gray-500 line-through">${plan.originalPrice}</span>
										<span className="ml-2 text-gray-600">{plan.period}</span>
									</div>

									<button className={`w-full rounded px-4 py-3 font-medium ${plan.buttonClass}`}>
										{plan.buttonText}
									</button>
								</div>

								<div className="mb-6">
									<div className="mb-4 text-center text-sm text-gray-600">交易佣金: {plan.commission}</div>
								</div>

								<ul className="space-y-3">
									{plan.features.map((feature, idx) => (
										<li key={idx} className="flex items-center text-sm text-gray-700">
											<svg
												className="mr-3 h-4 w-4 flex-shrink-0 text-green-500"
												fill="currentColor"
												viewBox="0 0 20 20"
											>
												<path
													fillRule="evenodd"
													d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
													clipRule="evenodd"
												/>
											</svg>
											{feature}
										</li>
									))}
								</ul>
							</div>
						))}
					</div>
				</div>
			</section>

          <section className="py-16 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">为什么选择品店?</h2>
          <p className="text-lg text-gray-600">全方位的电商解决方案，助力您的业务增长</p>
        </div>

        <div className="  rounded-lg p-8">
          {/* Table Header */}
          <div className="grid grid-cols-4 gap-4 mb-6 bg-white rounded-lg p-4 shadow-sm">
            <div className="font-semibold text-gray-900">功能对比</div>
            <div className="text-center font-semibold text-gray-900">基础版</div>
            <div className="text-center font-semibold text-gray-900">进阶版</div>
            <div className="text-center font-semibold text-gray-900 bg-red-50 rounded px-2 py-1">PRO版</div>
          </div>

          {/* Feature Categories */}
          {features.map((category, categoryIndex) => (
            <div key={categoryIndex} className="mb-8">
              <h3 className="font-semibold text-lg text-gray-900 mb-4 px-4">{category.category}</h3>
              {category.items.map((item, itemIndex) => (
                <div key={itemIndex} className="grid grid-cols-4 gap-4 py-3 px-4 bg-white mb-2 rounded-lg  hover:shadow-md transition-shadow">
                  <div className="text-gray-700">{item.name}</div>
                  <div className="text-center">{renderValue(item.basic)}</div>
                  <div className="text-center">{renderValue(item.advanced)}</div>
                  <div className="text-center bg-red-50 rounded px-2 py-1">{renderValue(item.pro)}</div>
                </div>
              ))}
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <button className="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-lg font-medium">
            免费试用
          </button>
        </div>
      </div>
    </section>
    </>
  )
}
