"use client";

import { Link } from "@/navigation";
import { useEffect, useState, useCallback, useMemo } from "react";
import { ChevronDown } from "lucide-react";
import { useTranslations } from "next-intl";
import { useParams } from "next/navigation";
import { ProductCategoriesListQuery } from "@/gql/graphql";
import { defaultLocale } from "@/config";

type CategoryEdge = ProductCategoriesListQuery["categories"]["edges"][0];

const CategoryItem = ({
	locale,
	item,
	level = 0,
	currentSlug,
	expandedCategories,
	toggleCategory,
}: {
	locale?: string;
	item: CategoryEdge;
	level?: number;
	currentSlug: string;
	expandedCategories: string[];
	toggleCategory: (id: string) => void;
}) => {
	const hasChildren = item.node.children?.edges?.length > 0;
	const isExpanded = expandedCategories.includes(item.node.id);
	const isActive = item.node.slug === currentSlug;

	return (
		<div className="relative">
			<div
				className={`group flex items-center justify-between rounded-md p-2
					transition-all duration-200 
					${isActive ? "bg-blue-50/80" : "hover:bg-gray-50/80"}
					${isExpanded && !isActive ? "bg-gray-50/50" : ""}`}
				style={{ paddingLeft: `${level * 0.5 + 0.5}rem` }}
			>
				<div className="flex items-center gap-2">

					<Link
						href={`/products/${item.node.slug}`}
						className={`text-sm transition-colors duration-200 
							${isActive ? "font-medium text-blue-600" : "text-gray-600 hover:text-gray-900 group-hover:text-gray-900"}`}
					>
						{locale === defaultLocale ? item.node.name : item.node.translation?.name || item.node.name}
					</Link>
				</div>
        {hasChildren && (
						<button
							onClick={() => toggleCategory(item.node.id)}
							className={`flex h-5 w-5 items-center justify-center rounded-full
								transition-all duration-200 
								${isActive ? "hover:bg-blue-100" : "hover:bg-gray-200/80"}
								${isExpanded ? "bg-gray-100" : ""}`}
						>
							<div
								className={`transform transition-transform duration-200 
								${isExpanded ? "rotate-180" : "rotate-0"}`}
							>
								<ChevronDown
									className={`h-3.5 w-3.5 
									${isActive ? "text-blue-500" : "text-gray-500"}
									${isExpanded ? "text-gray-600" : ""}`}
								/>
							</div>
						</button>
					)}
			</div>

			{hasChildren && (
				<div
					className={`grid transition-all duration-300 ease-in-out
						${isExpanded ? "grid-rows-[1fr]" : "grid-rows-[0fr]"}`}
				>
					<div className="overflow-hidden">
						<div className={`space-y-0.5 ${level < 2 ? "ml-4 border-l border-gray-100 pl-2" : ""}`}>
							{item.node.children?.edges.map((child: CategoryEdge) => (
								<CategoryItem
									key={child.node.id}
									item={child}
									level={level + 1}
									currentSlug={currentSlug}
									expandedCategories={expandedCategories}
									toggleCategory={toggleCategory}
								/>
							))}
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

type CategoriesProps = ProductCategoriesListQuery["categories"];

const findParentCategoryIds = (
	categories: CategoryEdge[] | undefined,
	targetSlug: string,
	parentIds: string[] = [],
): string[] => {
	if (!categories) return [];

	return categories.reduce((acc, category) => {
		if (category.node.slug === targetSlug) return parentIds;

		if (category.node.children?.edges?.length) {
			const result = findParentCategoryIds(category.node.children.edges, targetSlug, [
				...parentIds,
				category.node.id,
			]);
			if (result.length) return result;
		}
		return acc;
	}, [] as string[]);
};

const getSiblingCategories = (categories: CategoryEdge[] | undefined, targetId: string): string[] => {
	if (!categories) return [];

	const findParent = (
		items: CategoryEdge[],
		id: string,
	): { parent: CategoryEdge | null; siblings: string[] } => {
		for (const item of items) {
			// 如果是顶级类别
			if (item.node.id === id) {
				return {
					parent: null,
					siblings: items.map((cat) => cat.node.id).filter((catId) => catId !== id),
				};
			}

			// 在子类别中查找
			if (item.node.children?.edges) {
				for (const child of item.node.children.edges) {
					if (child.node.id === id) {
						return {
							parent: item,
							siblings: item.node.children.edges.map((cat) => cat.node.id).filter((catId) => catId !== id),
						};
					}
				}
				const result = findParent(item.node.children.edges, id);
				if (result.parent || result.siblings.length) return result;
			}
		}
		return { parent: null, siblings: [] };
	};

	return findParent(categories, targetId).siblings;
};

const Categories = ({ locale, categoriesData }: { locale?: string; categoriesData: CategoriesProps }) => {
	const t = useTranslations();
	const { slug: currentSlug } = useParams();
	const [expandedCategories, setExpandedCategories] = useState<string[]>([]);

	useEffect(() => {
		if (currentSlug && categoriesData) {
			setExpandedCategories(findParentCategoryIds(categoriesData.edges, currentSlug as string));
		}
	}, [locale, currentSlug, categoriesData]);

	const toggleCategory = useCallback(
		(categoryId: string) => {
			setExpandedCategories((prev) =>
				prev.includes(categoryId)
					? prev.filter((id) => id !== categoryId)
					: [
							...prev.filter((id) => !getSiblingCategories(categoriesData.edges, categoryId).includes(id)),
							categoryId,
						],
			);
		},
		[categoriesData],
	);
    const sortedCategories = useMemo(() => {
      if (!categoriesData?.edges) return [];
      return [...categoriesData.edges].sort((a:any,b:any) => {
        if (!a.node.sortNumber && b.node.sortNumber) return 1;
        if (a.node.sortNumber && !b.node.sortNumber) return -1;
        if (!a.node.sortNumber && !b.node.sortNumber) return 0;
        return a.node.sortNumber - b.node.sortNumber;
      });
    }, [categoriesData]);
  

	return (
		<div className="overflow-hidden rounded-lg border bg-white shadow-sm">
			<div className="border-b bg-gray-50/50 px-4 py-3">
				<h3 className="text-base font-medium text-gray-800">{t("blog.Categories")}</h3>
			</div>
			<div className="overflow-y-auto p-3">
				<div className="space-y-0.5">
					{sortedCategories.length>0&&sortedCategories.map((item) => (
						<CategoryItem
							key={item.node.id}
							item={item}
							currentSlug={currentSlug as string}
							expandedCategories={expandedCategories}
							toggleCategory={toggleCategory}
						/>
					))}
				</div>
			</div>
		</div>
	);
};

export default Categories;
