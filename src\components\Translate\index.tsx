"use client";
import { TLocale } from "@/lib/@types/locale";
import { SITE_LOCALES } from "@/lib/constant";
import React, { useEffect, useState, useCallback } from "react";
import { usePathname } from "next/navigation";
import { useLocale } from "next-intl";
import { defaultLocale } from "@/config";
import { Link } from "@/navigation";
import FlagIcon from "@/components/Flag";
import { Popover } from "antd";

const Translate = ({ lastScrollPosition }: { lastScrollPosition?: number }) => {
	const currentLocales: TLocale[] = SITE_LOCALES;
	const [isOpenLanguage, setIsOpenLanguage] = useState(false);
	const pathname = usePathname();
	const locale = useLocale();
	const [currentSlug, setCurrentSlug] = useState("");

	const getCurrentLocale = useCallback(() => {
		return currentLocales.find((item) => item.code === locale);
	}, [currentLocales, locale]);

	const closeLanguageMenu = useCallback(() => {
		setIsOpenLanguage(false);
	}, []);

	useEffect(() => {
		if (pathname) {
			setCurrentSlug(locale === defaultLocale ? pathname : pathname.replace(locale, ""));
		}
	}, [pathname, locale]);

	if (process.env.NEXT_PUBLIC_IS_I18N !== "true") {
		return null;
	}

	// 语言选择内容
	const languageContent = (
		<div className="py-1 min-w-[160px]">
			{currentLocales.map((item) => (
				<Link
					key={item.code}
					href={currentSlug}
					locale={item.code}
					className="flex items-center gap-2 whitespace-nowrap px-3 py-2 text-sm text-gray-600 transition-colors hover:bg-gray-50 hover:text-gray-900 rounded-md"
					onClick={closeLanguageMenu}
				>
					<FlagIcon flag={item.flag} />
					<span>{item.nativeName}</span>
					{locale === item.code && (
						<svg
							className="ml-auto h-4 w-4 text-blue-500"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
							aria-hidden="true"
						>
							<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
						</svg>
					)}
				</Link>
			))}
		</div>
	);

	return (
		<div className="w-full h-full !text-right">
			<Popover
				content={languageContent}
				trigger="click"
				placement="topRight"
				open={isOpenLanguage}
				onOpenChange={setIsOpenLanguage}
				overlayClassName="translate-popover"
				arrow={false}
			>
				<button
					className={`flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium transition-colors hover:bg-gray-100 ${
						lastScrollPosition > 0 ? "text-black" : "text-black"
					}`}
					aria-expanded={isOpenLanguage}
					aria-haspopup="true"
				>
					<FlagIcon flag={getCurrentLocale()?.flag || ""} />
					<span>{getCurrentLocale()?.nativeName}</span>
					<svg
						className={`h-4 w-4 transition-transform ${
							isOpenLanguage ? "rotate-0" : "rotate-180"
						}`}
						fill="none"
						viewBox="0 0 24 24"
						stroke="currentColor"
						aria-hidden="true"
					>
						<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
					</svg>
				</button>
			</Popover>
		</div>
	);
};

export default Translate;
