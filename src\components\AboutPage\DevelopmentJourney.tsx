"use client"
import React, { useState, useRef, useEffect } from 'react';
import { Swiper, SwiperSlide } from "swiper/react";
import { Controller, FreeMode, EffectFade } from "swiper/modules";
import "swiper/css";
import "swiper/css/free-mode";
import "swiper/css/effect-fade";

export default function DevelopmentJourney() {
    const [activeYear, setActiveYear] = useState(0);
    const [progressWidth, setProgressWidth] = useState('0px');
    const timelineRef = useRef<HTMLDivElement>(null);
    const nodeRefs = useRef<(HTMLDivElement | null)[]>([]);
    const [imageSwiper, setImageSwiper] = useState<any>(null);
    const [textSwiper, setTextSwiper] = useState<any>(null);
    const [canGoPrev, setCanGoPrev] = useState(false);
    const [canGoNext, setCanGoNext] = useState(true);

    const years = [
        { year: 2017, image: "/image/default-image.webp", desc: ["公司成立，组建核心团队。", "启动研发首款产品。"] },
        { year: 2018, image: "/image/default-image.webp", desc: ["推出首款产品，进入国内市场。", "获得首批客户反馈。"] },
        { year: 2019, image: "/image/default-image.webp", desc: ["完成首轮融资。", "团队扩张至50人。", "产品迭代优化。"] },
        { year: 2020, image: "/image/default-image.webp", desc: ["适应疫情，推出在线解决方案。", "实现远程协作功能。"] },
        { year: 2021, image: "/image/default-image.webp", desc: ["进入国际市场。", "与海外伙伴合作。", "销售额增长200%。"] },
        { year: 2022, image: "/image/default-image.webp", desc: ["技术突破，获得专利。", "荣获行业奖项。"] },
        { year: 2023, image: "/image/default-image.webp", desc: ["启动可持续发展项目。", "减少碳排放举措。", "社区合作计划。"] },
        { year: 2024, image: "/image/default-image.webp", desc: ["发布新战略规划。", "持续创新研发。"] },
    ];

    const handleYearClick = (index: number) => {
        setActiveYear(index);
        if (imageSwiper) imageSwiper.slideTo(index);
        if (textSwiper) textSwiper.slideTo(index);
    };

    const handleSlideChange = (swiper: any) => {
        const newIndex = swiper.realIndex;
        setActiveYear(newIndex);
        if (swiper === imageSwiper && textSwiper) textSwiper.slideTo(newIndex);
        if (swiper === textSwiper && imageSwiper) imageSwiper.slideTo(newIndex);
    };

    useEffect(() => {
        const updateWidth = () => {
            if (timelineRef.current && nodeRefs.current[activeYear]) {
                if (activeYear === 0) {
                    setProgressWidth('0px');
                } else {
                    const timelineRect = timelineRef.current.getBoundingClientRect();
                    const nodeRect = nodeRefs.current[activeYear].getBoundingClientRect();
                    const left = nodeRect.left - timelineRect.left;
                    setProgressWidth(`${left}px`);
                }
            }
        };

        updateWidth();
        window.addEventListener('resize', updateWidth);
        return () => window.removeEventListener('resize', updateWidth);
    }, [activeYear]);

    useEffect(() => {
        if (imageSwiper) {
            setCanGoPrev(imageSwiper.activeIndex > 0);
            setCanGoNext(imageSwiper.activeIndex < years.length - 1);
        }
    }, [activeYear, imageSwiper]);

    const handlePrev = () => {
        if (canGoPrev) {
            const newIndex = activeYear - 1;
            setActiveYear(newIndex);
            imageSwiper?.slideTo(newIndex, 300);
            textSwiper?.slideTo(newIndex, 300);
        }
    };

    const handleNext = () => {
        if (canGoNext) {
            const newIndex = activeYear + 1;
            setActiveYear(newIndex);
            imageSwiper?.slideTo(newIndex, 300);
            textSwiper?.slideTo(newIndex, 300);
        }
    };

    return (
        <div className="bg-[#5959ff] md:py-[120px] py-[60px] flex items-center">
            <div className="max-w-[1920px] mx-auto md:px-[160px] px-[24px] text-white">
                <h2 className="md:text-3xl max-lg:text-center text-2xl font-bold mb-10">发展历程</h2>
                <div ref={timelineRef} className="relative flex justify-between mb-[100px] max-md:hidden">
                    {/* 背景横线：全长，#7a7aff，位置调整为穿过节点中心 */}
                    <div className="absolute left-0 right-0 h-[2px] bg-[#7a7aff]" style={{ top: 'calc(5px)' }}></div>
                    {/* 前景横线：覆盖到当前激活年份的节点中心，白色 */}
                    <div 
                        className="absolute left-0 h-[2px] bg-white transition-all duration-300" 
                        style={{ top: 'calc(5px)', width: progressWidth }}
                    ></div>
                    {years.map((item, index) => (
                        <div 
                            key={index} 
                            ref={(el) => (nodeRefs.current[index] = el)}
                            className="flex flex-col-reverse relative z-10"
                        >
                            <button
                                onClick={() => handleYearClick(index)}
                                className={`text-lg mt-2 ${index <= activeYear ? 'text-white font-bold' : 'text-[#7A7AFF] opacity-70'}`}
                            >
                                {item.year}
                            </button>
                            <div className={`w-[10px] h-[10px] rounded-[2px] ${index <= activeYear ? 'bg-white' : 'bg-[#7A7AFF]'}`}></div>
                        </div>
                    ))}
                </div>
                <div className="grid items-start grid-cols-1 md:grid-cols-2 gap-20 md:h-[720px] h-auto">
                    {/* 左 Swiper: 图片区域，滑动拖拽 */}
                    <Swiper
                        modules={[FreeMode, Controller]}
                        freeMode={{ enabled: true, sticky: true, momentumRatio: 0.5 }} // 减少惯性，确保稳定snap
                        loop={false}
                        slidesPerView={1}
                        spaceBetween={0}
                        speed={300}
                        className="w-full overflow-hidden select-none cursor-grab active:cursor-grabbing"
                        controller={{ control: textSwiper }}
                        onSwiper={(swiper) => setImageSwiper(swiper)}
                        onSlideChangeTransitionEnd={(swiper) => handleSlideChange(swiper)} // 只在过渡结束时更新
                    >
                        {years.map((item, index) => (
                            <SwiperSlide key={index}>
                                <img src={item.image} alt={`发展历程 ${item.year}`} className="w-full  aspect-square object-cover" />
                            </SwiperSlide>
                        ))}
                    </Swiper>
                    {/* 右 Swiper: 文案区域，淡入淡出效果 */}
                    <div className="w-full md:h-full h-[500px] relative">
                        <Swiper
                            modules={[EffectFade, Controller]}
                            effect="fade"
                            fadeEffect={{ crossFade: true }}
                            loop={false}
                            slidesPerView={1}
                            spaceBetween={0}
                            speed={300}
                            noSwiping={true} // 禁用拖拽                          
                            controller={{ control: imageSwiper }}
                            onSwiper={(swiper) => setTextSwiper(swiper)}
                            onSlideChangeTransitionEnd={(swiper) => handleSlideChange(swiper)} // 只在过渡结束时更新
                        >
                            {years.map((item, index) => (
                                <SwiperSlide key={index} className='h-full'>
                                    <h3 className="lg:text-[120px] text-[60px] leading-[0.85] font-semibold mb-4">{item.year}</h3>
                                    <div className='flex flex-col gap-4 mt-10'>
                                    {item.desc.map((line, idx) => (
                                        <p 
                                            key={idx} 
                                            className={`py-5 border-t border-white/20 text-[16px] ${idx === item.desc.length - 1 ? 'border-b border-white/50' : ''}`}
                                        >
                                            {line}
                                        </p>
                                    ))}
                                    </div>
                                </SwiperSlide>
                            ))}
                        </Swiper>
                        {/* 自定义左右按钮，在左下角 */}
                        <div className="absolute bottom-[60px] left-0 flex gap-2">
                            <button 
                                onClick={handlePrev} 
                                disabled={!canGoPrev}
                                className={`w-12 h-12 rounded-full bg-white/50 flex items-center justify-center ${!canGoPrev ? 'opacity-50 cursor-not-allowed' : 'hover:bg-white/70'}`}
                            >
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M15 18L9 12L15 6" stroke="black" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                            </button>
                            <button 
                                onClick={handleNext} 
                                disabled={!canGoNext}
                                className={`w-12 h-12 rounded-full bg-white/50 flex items-center justify-center ${!canGoNext ? 'opacity-50 cursor-not-allowed' : 'hover:bg-white/70'}`}
                            >
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M9 18L15 12L9 6" stroke="black" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
