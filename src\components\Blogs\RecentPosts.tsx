import React from "react";
import Image from "next/image";
import { Tag } from "antd";
import { Link } from "@/navigation";
import { type Blog } from "@/lib/@types/api/blog";

interface RecentPostsProps {
	blogList: any[];
}

const RecentPosts: React.FC<RecentPostsProps> = ({ blogList }) => {
	return (
		<div className="recent border-line mt-6 border-b pb-8 md:mt-10">
			<div className="heading6 pb-4">Recent Posts</div>
			<div className="list-recent flex flex-col gap-4 pt-1">
				{blogList.slice(0, 5).map((item) => (
					<Link
						href={`/blog/${item.blog_slug}`}
						className="item mt-2 flex cursor-pointer flex-row items-center gap-4 text-gray-900"
						key={item.blog_slug}
					>
						<div className="flex min-w-[40px] flex-col items-center pr-2">
							{/* 上面显示日，两位，下面显示月三字母 */}
							{(() => {
								const date = item.blog_upload_time ? new Date(item.blog_upload_time) : null;
								if (!date) return null;
								const day = date.getDate().toString().padStart(2, "0");
								const month = date.toLocaleString("en-US", { month: "short" });
								return (
									<>
										<span className="text-lg  leading-none text-gray-700">{day}</span>
										<span className="mt-1 text-xs uppercase text-gray-400">{month}</span>
									</>
								);
							})()}
						</div>
						<div className="text-title hover:text-primary line-clamp-2 text-gray-700 transition-colors">
							{item.blog_title}
						</div>
					</Link>
				))}
			</div>
		</div>
	);
};

export default RecentPosts;
