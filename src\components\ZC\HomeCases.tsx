"use client";
import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination } from "swiper/modules";
import "swiper/css";
import { useTranslations } from "next-intl";
import { Link } from "@/navigation";
import Image from "next/image";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
export default function HomeCases() {
	const partners = [
		{ src: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/idx_case_1.jpg", alt: "CA" },
		{ src: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/idx_case_2.jpg", alt: "Klarna" },
		{ src: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/idx_case_3.jpg", alt: "菜鸟" },
		{ src: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/idx_case_4.jpg", alt: "OPay" },
	];

	return (
		<section className="bg-[#DDEEFF] py-[60px] md:py-[120px]">
			<div className="container relative">
				<div className="w-full">
					{/* 标题 */}
					<h2 className=" mb-[60px] text-center text-[36px] max-lg:text-[24px] leading-tight font-bold text-titleLight">
						快速搭建电商网站，即刻实现品牌出海
					</h2>
					{/* 内容 */}
					<div className="relative">
						<Swiper
							modules={[Autoplay, Pagination]}
							autoplay={{
								delay: 3000,
								disableOnInteraction: false,
							}}
              loop
							speed={500}
							pagination={{
								clickable: true,
								el: `.pagination2`,
								bulletClass: "bullet2",
								bulletActiveClass: "active2",
							}}
							centeredSlides
							breakpoints={{
								640: {
									slidesPerView: 1,
									spaceBetween: 16,
								},
								1024: {
									slidesPerView: 3,
									spaceBetween: 16,
								},
							}}
              className="swiper-cases"
						>
							{partners.map((item, idx) => (
								<SwiperSlide key={idx} className="px-4 pb-[20px]">
									<div className="relative h-[600px] rounded-lg overflow-hidden">
										<SEOOptimizedImage
											src={item.src}
											alt={item.alt}
											width={1000}
											height={1000}
											className="h-full w-full object-cover object-top"
										/>
									</div>
								</SwiperSlide>
							))}
						</Swiper>

						{/* 指示器 */}
						<div className="pagination2" />
					</div>
				</div>
			</div>
			{/* 自定义样式 */}
			<style jsx global>{`
				.pagination2 {
					display: flex;
					justify-content: center;
					align-items: center;
					margin-top: 60px;
					gap: 8px;
				}

				.bullet2 {
					width: 26px;
					height: 4px;
					background-color: #4c5d66;
					opacity: 0.25;
					transition: all 0.3s ease;
					cursor: pointer;
					border-radius: 0;
				}

				.active2 {
					background-color: #ff0000;
					opacity: 1;
				}
			`}</style>
		</section>
	);
}
