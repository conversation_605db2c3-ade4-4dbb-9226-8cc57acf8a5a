import { type Metadata } from "next";
import React from "react";
import { getBasePageSeo } from "@/lib/api/seo";
import { generateSeo } from "@/lib/utils/seo";
import { type MyPageProps } from "@/lib/@types/base";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { unstable_setRequestLocale } from "next-intl/server";
import { CountUpNumber } from "@/components/CountUpNumber/page";
import { Tracing } from "@/components/AboutUs/TracingBeam";
import AboutPage from "@/components/AboutPage";
import SolutionPage from "@/components/Solution";
/**
 * 生成页面的元数据
 * @param props 页面的属性，用于生成SEO信息
 * @returns 返回页面的元数据，包括SEO信息
 */
export const generateMetadata = async (props: MyPageProps): Promise<Metadata> => {
	props.params.page = ["solution"];
	// 获取基础页面的SEO数据
	const seo = await getBasePageSeo(props);
	// 生成最终的SEO信息，并返回
	return generateSeo(props, {
		...seo,
		ogType: "website",
	});
};

export default function Solution(props: MyPageProps) {
	unstable_setRequestLocale(props.params.locale);

	return (
			<SolutionPage />
	);
}
