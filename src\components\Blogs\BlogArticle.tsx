"use client";

import React, { useEffect, useRef, useState } from "react";
import styled from "styled-components";
import { useLocale, useTranslations } from "next-intl";
import { ClockCircleFilled, ProfileOutlined } from "@ant-design/icons";
import BlogSearch from "@/components/Blogs/BlogSeach.tsx";
import { Blog } from "@/lib/@types/api/blog";
import { Link } from "@/navigation.ts";
import { Avatar } from "antd";
import moment from "moment";
import FollowUs from "../Social/FollowUs";
import Categories from "./Categories";
import FeatureBlogs from "./FeaturedBlogs";
import { SocialShare } from "@/components/Other/Share";

// 使用 TailwindCSS 的一些自定义样式来优化文章显示
export const StyledArticle = styled.article`
	/* 定义与全局无关的样式 */
	p {
		display: block;
		margin-block-start: 1em;
		margin-block-end: 1em;
		margin-inline-start: 0px;
		margin-inline-end: 0px;
		unicode-bidi: isolate;
	}

	h1 {
		color: #18181b;
		font-weight: 700;
		font-size: 28px;
		padding: 4px 0;
	}

	/* 列表样式 */
	ul {
		list-style-type: disc;
		margin-left: 20px;
		padding-left: 0;
	}

	li {
		margin-bottom: 10px;
	}

	strong {
		padding: 0 2px;
	}

	/* 标题样式 */
	h2 {
		color: #18181b;
		font-weight: 700;
		font-size: 24px;
		padding: 4px 0;
	}

	h3 {
		color: #18181b;
		font-weight: 700;
		font-size: 20px;
		padding: 4px 0;
	}

	h4 {
		color: #18181b;
		font-weight: 600;
		font-size: 16px;
		padding: 4px 0;
	}

	h5 {
		color: #18181b;
		font-weight: 600;
		font-size: 14px;
		padding: 4px 0;
	}

	h6 {
		color: #18181b;
		font-weight: 500;
		font-size: 13px;
		padding: 4px 0;
	}

	span {
		color: #18181b;
		font-weight: 400;
		font-size: 16px;
		padding: 4px 0;
	}

	/* 表格样式 */
	table {
		width: 100%;
		border-collapse: collapse;
		margin-top: 1em;
		margin-bottom: 1em;
	}

	th,
	td {
		border: 1px solid #dcdcdc;
		padding: 8px;
		text-align: left;
	}

	/* 表格内文字 */
	table p {
		margin: 0;
	}
	a {
		padding: 0 2px;
	}
	img {
		display: inline-block;
		white-space: nowrap;
	}

	/* 添加其他必要的样式，确保不被全局样式覆盖 */
`;

const BlogArticle = ({
	content,
	blogList,
	blog,
	clsList,
}: {
	content: string;
	blogList: Blog.BlogListItem[];
	blog?: any;
	clsList?: any;
}) => {
	const [headings, setHeadings] = useState<string[]>([]);
	const [newContent, setNewContent] = useState<string>(content); // 用于存储修改后的内容

	// 处理锚点点击，添加偏移量避免被导航栏遮挡
	const handleAnchorClick = (e: React.MouseEvent<HTMLAnchorElement>, headingId: string) => {
		e.preventDefault();
		const targetElement = document.getElementById(headingId);
		if (targetElement) {
			const offset = 120; // 导航栏高度 + 额外间距
			const elementPosition = targetElement.getBoundingClientRect().top;
			const offsetPosition = elementPosition + window.pageYOffset - offset;

			window.scrollTo({
				top: offsetPosition,
				behavior: "smooth",
			});
		}
	};

	// 提取文章的 H1-H5 标题并生成目录，同时更新 newContent
	useEffect(() => {
		const parser = new DOMParser();
		const doc = parser.parseFromString(content, "text/html");

		// 提取标题并生成 ID
		const foundHeadings = Array.from(doc.querySelectorAll("h1, h2, h3, h4, h5")).map((h, index) => {
			const headingText = h.textContent || "";
			const headingId = headingText.trim().toLowerCase().replace(/\s+/g, "-"); // 将空格转义为 "-"
			h.id = headingId; // 给每个标题元素设置 ID
			return headingText; // 返回标题文本以供目录使用
		});

		// 更新内容为带有 ID 的 HTML
		const updatedContent = new XMLSerializer().serializeToString(doc); // 将修改后的文档序列化为 HTML
		setNewContent(updatedContent); // 更新 newContent
		setHeadings(foundHeadings); // 更新目录
	}, [content]);

	// 为页面添加scroll-padding-top样式，确保锚点导航不被导航栏遮挡
	useEffect(() => {
		const originalScrollPaddingTop = document.documentElement.style.scrollPaddingTop;
		document.documentElement.style.scrollPaddingTop = "120px";

		return () => {
			// 组件卸载时恢复原样式
			document.documentElement.style.scrollPaddingTop = originalScrollPaddingTop;
		};
	}, []);

	const t = useTranslations();
	let locale = useLocale();
	return (
		<>
			<div className="relative flex justify-between !gap-5   max-md:flex-wrap ">
				<div className="flex-1 ">
					<section className="container flex w-full justify-between gap-x-5 max-md:flex-wrap">
						<div className="flex w-full flex-col">
							<h2 className="w-full py-5 text-center text-3xl font-medium tracking-wider max-md:py-2 max-md:text-3xl">
								{blog?.blog_title}
							</h2>
							<div className="mb-5  flex   w-full flex-col items-center gap-x-5 text-gray-500 max-md:mb-0">
								<ul className="my-2  flex items-center gap-x-2">
									{blog?.blog_classification_list.map((cls) => {
										return (
											<li
												className="rounded-sm  border-[1px]  border-[#000] px-2  py-1 text-xs text-black"
												key={cls.cls_id}
											>
												{cls.cls_name}
											</li>
										);
									})}
								</ul>

								<div>
									Posted by {process.env.NEXT_PUBLIC_COMPANY_NAME} On{" "}
									{moment(blog?.upload_time).format("MMM DD YYYY")}
								</div>
							</div>
							{/* <div className="flex h-full w-full flex-1 items-end justify-end max-md:justify-start max-md:pb-3">
								<FollowUs textClass="!text-main"></FollowUs>
							</div> */}
						</div>
					</section>
					{/* 左侧文章内容 */}
					<StyledArticle
						className="animate__animated  animate__fadeIn w-full flex-1 overflow-auto  overflow-y-auto"
						style={{
							scrollbarWidth: "none", // Firefox
							msOverflowStyle: "none", // IE 10+
						}}
					>
						{/* 渲染修改后的内容，并通过 dangerouslySetInnerHTML 来处理 */}
						<div
							className="blog-content w-full"
							dangerouslySetInnerHTML={{
								__html: newContent, // 渲染修改后的内容
							}}
						/>
					</StyledArticle>
				</div>

				{/*{isFixed&&<div className="w-[450px] "></div>}*/}

				{/* 右侧 Table of Contents (TOC) */}
				{/*<div className={` ${isFixed ? ' fixed top-[105px]  z-50 container w-full  flex justify-end' : ''}`}>*/}
				<div className={`w-[450px] max-lg:w-1/3 max-md:w-full`}>
					<div className="mb-6">
						<Categories clsList={clsList} />
					</div>

					{/* 锚点导航 - Table of Contents */}
					{headings.length > 0 && (
						<nav className="my-4 h-fit rounded border border-gray-300 bg-[#f9f9f9] p-4">
							<div className="mb-4 flex items-center justify-between">
								<h3 className="text-xl font-semibold">{t("blog.Contents")}</h3>
								<ProfileOutlined className="rounded-xl text-2xl text-gray-300" />
							</div>
							<ul className="space-y-2">
								{headings.map((heading, index) => {
									const headingId = heading.trim().toLowerCase().replace(/\s+/g, "-");
									return (
										<li key={index} className="cursor-pointer text-sm hover:underline">
											<span className="mr-1">{index + 1}.</span>
											<a
												className="text-black transition-colors hover:text-blue-600"
												href={`#${headingId}`}
												onClick={(e) => handleAnchorClick(e, headingId)}
											>
												{heading}
											</a>
										</li>
									);
								})}
							</ul>
						</nav>
					)}

					<section>
						<FeatureBlogs locale={locale}></FeatureBlogs>
					</section>

					<div className="action mt-5 flex flex-wrap items-center justify-between gap-5 pb-10   md:mt-8">
						<div className="left flex flex-wrap items-center gap-3">
							<p>Tag:</p>
							<ul className="list flex flex-wrap items-center gap-3">
								{blog?.blog_tag_list.map((tag) => {
									return (
										<li
											key={tag.tag_id}
											className={`tags text-button-uppercase cursor-pointer rounded-full bg-gray-200 px-4 py-1.5 duration-300 hover:bg-black hover:text-white`}
										>
											{tag.tag_name}
										</li>
									);
								})}
							</ul>
						</div>
						<div className="right flex flex-wrap items-center gap-3">
							<SocialShare />
						</div>
					</div>

					{/* <nav className={` border-gray-450 my-4 h-fit rounded border bg-[#f9f9f9] p-4 `}>
						<div className="mb-4 flex items-center justify-between">
							<h3 className="text-xl font-semibold ">{t("blog.Contents")}</h3>
							<ProfileOutlined className="rounded-xl text-2xl text-gray-300" />
						</div>
						<ul className="space-y-2">
							{headings.map((heading, index) => (
								<li key={index} className=" cursor-pointer text-sm hover:underline ">
									<span className="mr-1">{index + 1}.</span>
									<a className="text-black" href={`#${heading.trim().toLowerCase().replace(/\s+/g, "-")}`}>
										{heading}
									</a>
								</li>
							))}
						</ul>
					</nav>
					<BlogSearch></BlogSearch>
					<RelatedBlog blogList={blogList}></RelatedBlog> */}
				</div>

				{/*</div>*/}
			</div>
		</>
	);
};
const RelatedBlog = ({ blogList }: { blogList: Blog.BlogListItem[] }) => {
	const t = useTranslations();

	return (
		<>
			<h3 className={"my-4 border-b border-b-gray-400 py-4 text-xl"}>{t("blog.RelatedBlogs")}</h3>
			<ul>
				{blogList &&
					blogList.map((blog) => {
						return (
							<li key={blog.blog_id} className="border-b border-b-gray-400">
								<Link href={"/blog/" + blog.blog_slug}>
									<h3 className="my-2  line-clamp-2 !text-xl font-bold  text-black ">{blog.blog_title}</h3>
									{blog.blog_classification_list &&
										blog.blog_classification_list.map((item, index) => {
											return (
												index === 0 && (
													<div className=" py-4 text-xs  uppercase text-main" key={item.cls_slug}>
														{item.cls_name}
													</div>
												)
											);
										})}{" "}
								</Link>
							</li>
						);
					})}
			</ul>
		</>
	);
};

export default BlogArticle;
