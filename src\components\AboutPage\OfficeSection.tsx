import React from "react";
import { motion } from 'framer-motion';

const OfficeSection = () => {
  return (
    <section className="bg-[#FFA180] md:py-[120px] py-[60px]">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 md:gap-20 gap-12 items-center">
          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div>
              <h2 className="md:text-[40px] text-2xl font-bold text-gray-900 mb-6">
                我们的愿景
              </h2>
              <p className="text-[#222222] md:text-[16px] text-[14px] leading-relaxed">
                我们相信，通过提供一套全方位解决方案，我们可以帮助商家解决面临的各种挑战和问题。无论是提升销售效率、拓展销售渠道，还是优化库存管理和物流配送，我们的解决方案都能够为商家带来实质性的帮助和支持。我们致力于成为商家的长期合作伙伴，与他们共同成长，共同迈向成功之路。我们的团队，由充满激情和创新精神的专家组成，是推动这一愿景成为现实的核心力量，致力于不断探索和实现互联网零售领域的未来趋势。
              </p>
            </div>
          </motion.div>

          {/* Office Image */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            <img
              src="/image/abouts/10011.png"
              alt="Office Team"
              className="shadow-lg w-full"
            />
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default OfficeSection;