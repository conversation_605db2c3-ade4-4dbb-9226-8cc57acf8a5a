"use client";
import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination } from "swiper/modules";
import "swiper/css";
import { useTranslations } from "next-intl";
import { Link } from "@/navigation";
import Image from "next/image";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
export default function HomePartner() {
	const partners = [
		{ src: "/image/abouts/10012.jpg", alt: "CA" },
		{ src: "/image/abouts/10013.jpg", alt: "Klarna" },
		{ src: "/image/abouts/10014.jpg", alt: "菜鸟" },
		{ src: "/image/abouts/10015.png", alt: "OPay" },
		{ src: "/image/abouts/10016.jpg", alt: "Payoneer" },
		{ src: "/image/abouts/10017.jpg", alt: "万邑通" },
		{ src: "/image/abouts/10018.jpg", alt: "WINIT CORPORATION" },
		{ src: "/image/abouts/10019.jpg", alt: "WINIT CORPORATION" },
		{ src: "/image/abouts/10020.jpg", alt: "WINIT CORPORATION" },
		{ src: "/image/abouts/10021.jpg", alt: "WINIT CORPORATION" },
		{ src: "/image/abouts/10022.jpg", alt: "WINIT CORPORATION" },
	];

	return (
		<section className="py-[40px] md:py-[80px]">
			<div className="container relative">
				<div className="w-full">
					{/* 标题 */}
					<div className="mb-[60px] flex items-center justify-between">
						<div className="inner_title">
							<div className="title text-[36px] font-bold text-titleLight">生态伙伴</div>
						</div>
						<div className="group">
							<Link
								href="/about/honor"
								className="relative inline-block text-[18px] text-titleLight hover:text-[#555555]"
							>
								了解更多
								<span className="absolute bottom-0 left-0 h-[1px] w-0 bg-[#555555] transition-all duration-300 group-hover:w-full"></span>
								<i className="ri-arrow-right-s-line text-xl opacity-0 duration-300 group-hover:opacity-100"></i>
							</Link>
						</div>
					</div>
					{/* 内容 */}
					<div className="relative">
						<Swiper
							modules={[Autoplay, Pagination]}
							slidesPerGroup={4}
							autoplay={{
								delay: 3000,
								disableOnInteraction: false,
							}}
							speed={500}
							pagination={{
								clickable: true,
								el: `.pagination`,
								bulletClass: "bullet",
								bulletActiveClass: "active",
							}}
							breakpoints={{
								320: {
									slidesPerView: 2,
									spaceBetween: 12,
								},
								640: {
									slidesPerView: 2,
									spaceBetween: 16,
								},
								768: {
									slidesPerView: 3,
									spaceBetween: 20,
								},
								1024: {
									slidesPerView: 4,
									spaceBetween: 24,
								},
							}}
						>
							{partners.map((item, idx) => (
								<SwiperSlide key={idx} className="pb-[20px] px-4">
                  {/* style={{boxShadow:"3px 0px 10px rgba(0, 0, 0, 0.05)"}} */}
									<div className="relative h-[150px] shadow-md" >
										{/* 添加固定高度和relative定位 */}
										<SEOOptimizedImage
											src={item.src}
											alt={item.alt}
											width={1000}
											height={1000}
											className="w-full h-full object-contain" // 使用contain以保持比例不变形
										/>
									</div>
								</SwiperSlide>
							))}
						</Swiper>

						{/* 指示器 */}
						<div className="pagination" />
					</div>
				</div>
			</div>
			{/* 自定义样式 */}
			<style jsx global>{`
        .pagination {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 60px;
          gap: 8px;
        }
  
        .bullet {
          width: 26px;
          height: 4px;
          background-color: #4C5D66;
          opacity: 0.25;
          transition: all 0.3s ease;
          cursor: pointer;
          border-radius: 0;
        }
        
        .active {
          background-color: #ff0000;
          opacity: 1;
        }
      `}</style>
		</section>
	);
}
