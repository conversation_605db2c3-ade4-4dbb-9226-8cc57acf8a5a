import React from "react";
import HeroSection from "./HeroSection";
import PartnerImg from "./PartnerImg";
import SolutionFooter from "../Solution/SolutionFooter";
export default function Index() {
  	const partners1 = [
		{
			id: 1,
			name: "Google",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p1.jpg",
		},
		{
			id: 2,
			name: "<PERSON><PERSON>",
			logo: "https://www.shoplazza.cn/upload/ztyImg/2024-03/65e5a007b7dcc.png",
		},
		{
			id: 3,
			name: "AliExpress",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p3.jpg",
		},
		{
			id: 4,
			name: "PayPal",
			logo: "https://www.shoplazza.cn/upload/ztyImg/2024-04/661ce6bc57567.png",
		},
		{
			id: 5,
			name: "<PERSON><PERSON>",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p5.jpg",
		},
		{
			id: 6,
			name: "<PERSON>",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p6.jpg",
		},
		{
			id: 7,
			name: "Microsoft",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p7.jpg",
		},
		{
			id: 8,
			name: "Microsoft",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p8.jpg",
		},
		{
			id: 9,
			name: "Microsoft",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p9.jpg",
		},
		{
			id: 10,
			name: "Microsoft",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p10.jpg",
		},
		{
			id: 11,
			name: "Microsoft",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p11.jpg",
		},
		{
			id: 12,
			name: "Microsoft",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p12.jpg",
		},
		{
			id: 13,
			name: "Microsoft",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p13.jpg",
		},
	];
  	const partners2 = [
		{
			id: 14,
			name: "Google",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p14.jpg",
		},
		{
			id: 15,
			name: "Meta",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p15.jpg",
		},
		{
			id: 16,
			name: "AliExpress",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p16.jpg",
		},
		{
			id: 17,
			name: "PayPal",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p17.jpg",
		},
		{
			id: 18,
			name: "AWS",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p18.jpg",
		},
		{
			id: 19,
			name: "Microsoft",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p19.jpg",
		},
		{
			id: 20,
			name: "Microsoft",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p20.jpg",
		},
		{
			id: 21,
			name: "Microsoft",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p21.jpg",
		},
		{
			id: 22,
			name: "Microsoft",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p22.jpg",
		},
		{
			id: 23,
			name: "Microsoft",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p23.jpg",
		},
		{
			id: 24,
			name: "Microsoft",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p24.jpg",
		},
		{
			id: 25,
			name: "Microsoft",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_p25.jpg",
		},
	];
	return (
		<div className="pt-[64px]">
			<HeroSection />
			<div className="py-20">
				<div className="container">
					<PartnerImg title='我们的合作伙伴' description='与行业领先企业建立战略合作关系，共同为客户提供最优质的服务和解决方案' data={partners1} />
					<PartnerImg title='支付合作伙伴' description='我们与您的客户了解并信任的支付服务商合作，提供启动业务的解决方案。' data={partners2} />
				</div>
			</div>
       <SolutionFooter />
		</div>
	);
}
