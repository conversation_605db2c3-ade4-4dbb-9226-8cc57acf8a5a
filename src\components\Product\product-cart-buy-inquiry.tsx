"use client";
import React, { useEffect, useState } from "react";

import Skeleton from "react-loading-skeleton";
import Calculator from "@/components/Calculator";
import InquiryButton from "@/components/Inquiry/Button";

import { useProductVariableStore } from "@/lib/store/product-variable.store";
import { useProductAttributeStore } from "@/lib/store/product-attribute.store";
import { useShoppingCart } from "@/lib/hooks/useShoppingCart";
import { useUserStore } from "@/store/user.store";
import { setInquiry } from "@/lib/utils/util";
import { useShoppingCartStore } from "@/lib/store/shoppingCart";
import {useRouter  } from "@/navigation";
import { useUserAuth } from "@/lib/hooks/useUserAuth";
import { App } from "antd";
import { IconLove } from "./product-card";
import clsx from "clsx";
import Svg from "../Header/Menu/Svg";
import { useCompareStore } from "@/lib/store/Compare.store";
import CompareList from "../CompareList";
import { useLoveStore } from "@/lib/store/love.store";
import Price from "../Price/price";
import { useTranslations } from "next-intl";
export default function ProductCartBuyInquiry(props?: any) {
	const isProps = !!Object?.keys(props!)?.length;
	const [count, setCount] = useState(1);
	const { addToCart, createBuyBowCheckout } = useShoppingCart();
	let { userInfo } = useUserStore();
	const { currentProduct } = useProductVariableStore();
	const { selectAttribute } = useProductAttributeStore();
	const [minQuantity, setMinQuantity] = useState(-1);
	const [loading, setLoading] = useState(false);
  const t = useTranslations('message');

  //Inquiryloading
	const [Inquiryloading, setInquiryloading] = useState(false);

   //Cartloading
	const [Cartloading, setCartloading] = useState(false);

     //Buy Bowloading
	const [BuyBowloading, setBuyBowloading] = useState(false);
  
	const { setisCart, BuyBowcheckoutId, BuyBowCartList } = useShoppingCartStore() as any;
    let { compareIds, show, changeShow, setcCmpareProducts } = useCompareStore();
	const { isLogin } = useUserAuth();

      const { loveIds, setLoveProducts } = useLoveStore();
	let router = useRouter();
	// 将 message 替换为 App.useApp()
	const { message } = App.useApp();
	// 添加询盘
	async function AddInquiry() {
		if (!props.VariantActive) return;
		setInquiryloading(true);
		// @ts-ignore
		setTimeout(() => {
			setInquiry(props.product, count, props.VariantActive.name);

			return setInquiryloading(false);
		}, 2000);
	}

	// 添加购物车
	async function AddCart() {
    if(!props.VariantActive?.pricing?.price.gross.amount){
      return message.error(t('Thecurrent1234234'))

    }
		// if (!isLogin()) {
    //   const login = document.querySelector("#web-login") as HTMLLIElement;
    //   return  login.click();
    // };
		if (!props.VariantActive) return;
		setCartloading(true);
		// @ts-ignore
			await addToCart({
			channel: "default-channel",
			selectedVariantID: props.VariantActive.id,
			quantity: count,
		});
		 setCartloading(false);
	}

	//立即购买Buy Bow
	async function BuyBow() {
    if(!props.VariantActive?.pricing?.price.gross.amount){
      return message.error(t('Thecurrent1234234'))

    }
		// if (!isLogin()) {
    //   const login = document.querySelector("#web-login") as HTMLLIElement;
    //   return  login.click();
    // };
		if (!props.VariantActive) return;
		setBuyBowloading(true);
		// @ts-ignore
	let bool=	await createBuyBowCheckout({
			channel: "default-channel",
			selectedVariantID: props.VariantActive.id,
			quantity: count,
		});
		setBuyBowloading(false);
		setisCart(false);
    if(bool){
      router.push("/checkout");
    }

	}

  
            // 添加对比
            const addOrDelCompare = (event: React.MouseEvent, product) => {
                event.stopPropagation();
                console.log(product, "product");
        
                const id = product?.id;
                if (id) {
                    setcCmpareProducts(id);
                    changeShow(true)
                    
                }
        
            };

                const addOrDelLike = (event: React.MouseEvent) => {
                    event.stopPropagation(); //
                    const id = props.product.id;
                    if (id) {
                        setLoveProducts(id);
                    }
                };
	return (
		<>
			<div className="mt-5">
				{!isProps ? (
					<>
						<div className="s-flex gap-x-4">
							<Skeleton
								width={220}
								height={60}
								style={{
									borderRadius: "15px",
								}}
							></Skeleton>
							<Skeleton
								width={220}
								height={60}
								style={{
									borderRadius: "15px",
								}}
							></Skeleton>
						</div>
					</>
				) : (
					<div className="flex  flex-wrap justify-start gap-x-4 gap-y-4">
						{/* <InquiryButton count={count} id={props!.databaseId.toString()} />  */}
						<div className="flex flex-col  gap-y-4 flex-1">
							<div className="font-abeezee flex items-center  gap-2 ">
								<p className="!text-[16px]">Sub total:</p>
								{/* 此处的总价计算的是数量乘start  根据需要去改 */}
								<p className="!text-[18px] text-[#1e1e1e] ">
                  <Price price={props?.VariantActive?.pricing.price.gross.amount * count || ""} />
								</p>
							</div>
							<div className="flex  flex-wrap gap-4 w-full">
                {/* //数量 */}
                <div className=" flex w-full gap-4">
                <Calculator
									initCount={minQuantity > 0 ? minQuantity : 1}
									className="h-[30px] w-[45px]"
									changeCount={setCount}
									disabled={false}
									minCount={minQuantity > 0 ? minQuantity : 1}
									maxCount={props?.variations ? currentProduct?.stockQuantity : props?.stockQuantity}
								/>

            {/* 立即购买 */}
							{process.env.NEXT_PUBLIC_SITE_TYPE == "toc" && (
								<InquiryButton
									className="!font-poppins !w-full !rounded-sm border-[1px] border-black !bg-black !px-3  !py-2 !text-white  hover:!bg-white hover:!text-black"
									VariantActive={props.VariantActive}
									count={count}
									id={props.id}
									text={"form.Buy Now"}
									product={props.product}
									loading={BuyBowloading}
									callback={BuyBow}
								/>
                
							  )}
                </div>

                <div className="w-full flex gap-x-3">
            {/* 询盘 */}
                {
									<InquiryButton
										className="!font-poppins !flex-1 !rounded-sm border-[1px] border-black !bg-white !px-3 !py-2 !text-black hover:!bg-black hover:!text-white"
										VariantActive={props.VariantActive}
										count={count}
										id={props.id}
										text={"form.Inquiry Now"}
										product={props.product}
										loading={Inquiryloading}
										callback={AddInquiry}
									/>
								}

								{/* 购物车 */}
								{process.env.NEXT_PUBLIC_SITE_TYPE == "toc" && (
									<InquiryButton
										className="!font-poppins !flex-1   !rounded-sm border-[1px] border-black !bg-white !px-3 !py-2 !text-black hover:!bg-black hover:!text-white"
										VariantActive={props.VariantActive}
										count={count}
										id={props.id}
										text={"form.Add To Cart"}
										product={props.product}
										loading={Cartloading}
										callback={AddCart}
									/>
								)}
								<div className="flex gap-4">
									<div  onClick={addOrDelLike} className="group cursor-pointer flex h-[52px] w-[52px] items-center justify-center rounded-sm border-[1px] border-black bg-white transition-all duration-300 ">
									<IconLove product={props.product} />
									</div>
									<div onClick={(e) => addOrDelCompare(e, props.product)} className="group cursor-pointer flex h-[52px] w-[52px] items-center justify-center rounded-sm  border-[1px] border-black bg-white transition-all duration-300 ">
										{compareIds.includes(props.product.id) ? (
                                                                                <i className={clsx("ri-check-line text-[22px]")}></i>
                                                                            ) : (
                                                                                <Svg isScrolled={true} />
                                                                            )}
									</div>
								</div>



                </div>
							</div>

						</div>
					</div>
				)}
			</div>
<div className="max-md:hidden">
    <CompareList />
      </div>

		</>
	);
}

