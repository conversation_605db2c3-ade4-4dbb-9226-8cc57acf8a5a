import React from 'react'
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination } from "swiper/modules";
import "swiper/css";
import { useTranslations } from 'next-intl';
import { Link } from '@/navigation';
import Image from 'next/image';
import styles from './styles/EcologicalPartner.module.css';

export default function EcologicalPartner() {
  const partners = [
    { src: '/image/abouts/10012.jpg', alt: 'CA' },
    { src: '/image/abouts/10013.jpg', alt: '<PERSON>larna' },
    { src: '/image/abouts/10014.jpg', alt: '菜鸟' },
    { src: '/image/abouts/10015.png', alt: 'OPay' },
    { src: '/image/abouts/10016.jpg', alt: 'Payoneer' },
    { src: '/image/abouts/10017.jpg', alt: '万邑通' },
    { src: '/image/abouts/10018.jpg', alt: 'WINIT CORPORATION' },
    { src: '/image/abouts/10019.jpg', alt: 'WINIT CORPORATION' },
    { src: '/image/abouts/10020.jpg', alt: 'WINIT CORPORATION' },
    { src: '/image/abouts/10021.jpg', alt: 'WINIT CORPORATION' },
    { src: '/image/abouts/10022.jpg', alt: 'WINIT CORPORATION' },
  ];

  return (
    <section className='bg-white md:py-[120px]'>
      <div className='max-w-[1920px] mx-auto md:px-[160px] px-[24px] relative bg-[#FFA180]'>
        <div className='w-full md:py-[120px] py-[60px]'>
          {/* 标题 */}
          <div className="flex justify-between items-center mb-10">
            <div className="inner_title">
              <div className="title md:text-[40px] text-2xl font-bold text-[#222222]">生态伙伴</div>
            </div>
            <div className="group">
              <Link
                href="/partner"
                className="relative inline-block md:text-xl text-lg text-[#222222] hover:text-[#555555]"
              >
                了解更多
                <span className="absolute bottom-0 left-0 w-0 h-[1px] bg-[#555555] transition-all duration-300 group-hover:w-full"></span>
                <i className="ri-arrow-right-s-line text-xl group-hover:opacity-100 opacity-0 duration-300"></i>
              </Link>
            </div>
          </div>
          {/* 内容 */}
          <div className='md:mt-[60px] mt-[30px] relative'>
            {/* PC端轮播 - 隐藏于移动端 */}
            <div className="hidden md:block">
              <Swiper
                modules={[Autoplay,Pagination]}
                spaceBetween={36}
                slidesPerView={4}
                slidesPerGroup={4}
                autoplay={{
                  delay: 3000,
                  disableOnInteraction: false,
                }}
                speed={500}
                pagination={{
                  clickable: true,
                  el: `.${styles.pagination}`,
                  bulletClass: styles.bullet,
                  bulletActiveClass: styles.active,
                }}
                className='!pl-0 !pr-0'
              >
                {partners.map((item, idx) => (
                  <SwiperSlide key={idx} >
                    <div className='w-[373px] h-[186px] relative'>
                      <Image
                        src={item.src}
                        alt={item.alt}
                        fill
                        className='object-contain'
                      />
                    </div>
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>

            {/* 移动端轮播 - 隐藏于PC端 */}
            <div className="md:hidden">
              <Swiper
                modules={[Autoplay, Pagination]}
                spaceBetween={20}
                slidesPerView={1}
                autoplay={{
                  delay: 3000,
                  disableOnInteraction: false,
                }}
                speed={500}
                pagination={{
                  clickable: true,
                  el: `.${styles.pagination}`,
                  bulletClass: styles.bullet,
                  bulletActiveClass: styles.active,
                }}
                className='!pl-0 !pr-0'
              >
                {/* 将伙伴数组按每4个一组分组 */}
                {Array.from({ length: Math.ceil(partners.length / 4) }, (_, groupIndex) => {
                  const groupPartners = partners.slice(groupIndex * 4, groupIndex * 4 + 4);
                  return (
                    <SwiperSlide key={groupIndex}>
                      <div className="grid grid-cols-2 gap-4">
                        {groupPartners.map((item, idx) => (
                          <div key={idx} className='w-full h-[100px] relative'>
                            <Image
                              src={item.src}
                              alt={item.alt}
                              fill
                              className='object-contain'
                            />
                          </div>
                        ))}
                      </div>
                    </SwiperSlide>
                  );
                })}
              </Swiper>
            </div>
            
            {/* 指示器 - 共用 */}
            <div className={styles.pagination} />
          </div>
        </div>
      </div>
    </section>
  )
}

