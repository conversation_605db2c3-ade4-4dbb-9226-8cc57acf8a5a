"use client";
import React, { useEffect, useState, useRef } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination, Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";
import { Link } from "@/navigation";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import clsx from "clsx";
import Marquee from "react-fast-marquee";
// Enhanced data with content for each tab
const collections = [
	{
		node: {
			id: "1",
			slug: "t1",
			title: "Client 1",
			content: {
				title: "Client 1",
				description:
					"我们在 5 天内就上线了全新 SaaS 官网，Pinshop 提供的建站服务极快、稳定，SEO 也很省心。",
				quote: "我们坚信，只有坚持高品质产品和消费者认同的价值观，才能真正赢得全球消费者的认可和喜爱。",
				author: "FlexFlow CRM 市场总监 Anna D.",
				image: "https://www.shoplazza.cn/upload/ztyImg/2024-08/66bc90288d4d8.png",
			},
		},
	},
	{
		node: {
			id: "2",
			slug: "t2",
			title: "Client 2",
			content: {
				title: "Client 2",
				description:
					"我们的跨境电商站点上线后加载速度提升了 3 倍，几周内就有了 Google 收录，效果非常明显。",
				quote: "创新和品质是我们永恒的追求，为客户创造价值是我们的使命。",
				author: " HavenWear 创始人 David L.",
				image: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/evaluate_3.png",
			},
		},
	},
	{
		node: {
			id: "3",
			slug: "t3",
			title: "Client 3",
			content: {
				title: "Client 3",
				description:
					"我们需要一个多语种官网服务海外客户，Pinshop 快速搭建了支持三语的网站，并集成了 AI 视频与智能询盘，非常顺畅。",
				quote:
					"品店解决方案团队，帮助我们创建和执行了全球化的销售模式，连接我们与各种渠道和平台的关系，帮助我们将产品远销到20多个国家和地区。",
				author: "Jinglite Tools 销售总监 陈宇",
				image: "https://www.shoplazza.cn/upload/ztyImg/2024-08/66bc68985cb7a.png",
			},
		},
	},
	{
		node: {
			id: "4",
			slug: "t4",
			title: "Client 4",
			content: {
				title: "Client 4",
				description:
					"ICOICE 专注为全球年轻人打造更环保、更舒适、更个性的时尚美瞳品牌，其产品以大胆的风格和鲜明的颜色为特点，迎合了欧美消费者追求“活出自我”和“展现自我”风格的潮流趋势。现已成为海外畅销的 DTC 美瞳品牌，在全球范围内成功积累数十万忠实用户。",
				quote:
					"“面对全球多元市场，我们的策略十分简单而专注：深入理解每个市场，持续提供卓越服务，共赢消费者之心。”",
				author: "ICOICE 创始人",
				image: "https://www.shoplazza.cn/upload/ztyImg/2024-08/66bc75ed0eb62.png",
			},
		},
	},
	// {
	// 	node: {
	// 		id: "5",
	// 		slug: "t5",
	// 		title: "Flaxmaker",
	// 		content: {
	// 			title: "Flaxmaker",
	// 			description:
	// 				"Flaxmaker 拥抱包容性和多样化的泳装设计，迎合全尺寸的需求，致力于让每位女性都能在各种场合中展现自己的独特风格和魅力。自成立以来，其已拓展130多个国家，年销售额超千万美金，成功链接数十万消费者。",
	// 			quote:
	// 				"“Flaxmaker 的目的在于使她们的度假生活变得更美好，能留下更多难忘的回忆和照片，而泳衣只是策略和媒介。”",
	// 			author: "Flaxmaker Team",
	// 			image: "https://www.shoplazza.cn/upload/ztyImg/2024-08/66bc6c849e4e4.png",
	// 		},
	// 	},
	// },
	// Add more items as needed...
];
let zzarr = [
	{ img: "/image/img/zz/01.png" },
	{ img: "/image/img/zz/02.png" },
	{ img: "/image/img/zz/03.png" },
	{ img: "/image/img/zz/04.png" },
	{ img: "/image/img/zz/05.png" },
	{ img: "/image/img/zz/06.png" },
	{ img: "/image/img/zz/01.png" },
	{ img: "/image/img/zz/02.png" },
	{ img: "/image/img/zz/03.png" },
	{ img: "/image/img/zz/04.png" },
	{ img: "/image/img/zz/05.png" },
	{ img: "/image/img/zz/06.png" },
];
export default function HomeReview() {
	const [activeCollectionSlug, setActiveCollectionSlug] = useState(collections[0].node.slug);
	const [underlineStyle, setUnderlineStyle] = useState({ left: 0, width: 0 });
	const [swiperInstance, setSwiperInstance] = useState<any>(null);
	const tabsRef = useRef<(HTMLButtonElement | null)[]>([]);
	const textRefs = useRef<(HTMLSpanElement | null)[]>([]);

	useEffect(() => {
		const activeIndex = collections.findIndex(({ node }) => node.slug === activeCollectionSlug);

		if (activeIndex !== -1 && tabsRef.current[activeIndex]) {
			const activeTab = tabsRef.current[activeIndex]!;
			setUnderlineStyle({
				left: activeTab.offsetLeft,
				width: activeTab.offsetWidth,
			});

			if (swiperInstance) {
				swiperInstance.slideTo(activeIndex);
			}

			if (window.innerWidth < 768) {
				activeTab.scrollIntoView({
					behavior: "smooth",
					block: "nearest",
					inline: "center",
				});
			}
		}
	}, [activeCollectionSlug, swiperInstance]);

	const handleSwiperInit = (swiper: any) => {
		setSwiperInstance(swiper);
	};

	const handleSlideChange = (swiper: any) => {
		const newIndex = swiper.activeIndex;
		if (collections[newIndex]) {
			setActiveCollectionSlug(collections[newIndex].node.slug);
		}
	};
	const goNext = () => {
		if (swiperInstance) {
			swiperInstance.slideNext();
		}
	};

	const goPrev = () => {
		if (swiperInstance) {
			swiperInstance.slidePrev();
		}
	};
	return (
		<>
			<section className="bg-[#F2EADA] py-[60px] md:py-[120px]">
				<h2 className=" text-center text-[36px] font-bold leading-tight text-titleLight max-lg:text-[24px]">
					全球客户共同选择 Pinshop
				</h2>
				<p className="text-center text-titleLight/50 text-lg max-md:text-base mt-8 mb-[80px]">了解不同行业客户如何通过 Pinshop 快速上线官网，实现高效增长。</p>

				<div className="container relative mb-8 flex flex-nowrap items-center justify-start  gap-8 overflow-x-auto border-b border-titleLight/30 [-ms-overflow-style:none] [scrollbar-width:none] md:mb-12 [&::-webkit-scrollbar]:hidden">
					{collections.map(({ node }, index) => (
						<button
							ref={(el) => (tabsRef.current[index] = el)}
							key={node.id}
							onClick={() => setActiveCollectionSlug(node.slug)}
							className={clsx(
								"flex-1 whitespace-nowrap px-2 py-3 pb-6 text-sm uppercase tracking-wider text-titleLight transition-colors duration-300 md:px-4",
								{
									"font-bold": activeCollectionSlug === node.slug,
									"font-medium": activeCollectionSlug !== node.slug,
								},
							)}
						>
							<span
								ref={(el) => (textRefs.current[index] = el)}
								className="inline-block text-[22px] font-semibold max-md:text-[18px]"
							>
								{node.title}
							</span>
						</button>
					))}
					<div
						className="absolute bottom-[-1px] h-[5px] bg-mainPrimary transition-all duration-300 ease-in-out"
						style={{ left: underlineStyle.left, width: underlineStyle.width }}
					/>
				</div>

				{/* Swiper Carousel */}
				<div className="container relative">
					<Swiper
						onSwiper={handleSwiperInit}
						onSlideChange={handleSlideChange}
						initialSlide={0}
						spaceBetween={50}
						slidesPerView={1}
						loop
						modules={[Pagination, Navigation, Autoplay]}
						// autoplay={{ delay: 5000, disableOnInteraction: false }}
						className="review-swiper"
					>
						{collections.map(({ node }) => (
							<SwiperSlide key={node.id}>
								<div className="grid grid-cols-2 gap-10 max-lg:grid-cols-1">
									<div className="flex flex-col justify-center pr-40 max-lg:pr-0">
										<div>
											<h3 className="mb-6 text-3xl font-bold text-titleLight max-md:mb-4 max-md:text-xl">
												{node.content.title}
											</h3>

											<p className="mb-24 text-base leading-relaxed text-titleLight max-md:mb-6 max-md:text-base">
												{node.content.description}
											</p>

											<p className="text-lg leading-relaxed text-titleLight max-md:text-base">
												"{node.content.quote}"
											</p>

											<div className="my-10 text-right text-sm font-medium text-[#222222] max-md:text-xs">
												— {node.content.author}
											</div>

											<div className="group">
												<Link
													href="/about/honor"
													className="relative inline-block text-[18px] text-titleLight hover:text-[#555555]"
												>
													了解更多
													<span className="absolute bottom-0 left-0 h-[1px] w-0 bg-[#555555] transition-all duration-300 group-hover:w-full"></span>
													<i className="ri-arrow-right-s-line text-2xl duration-300"></i>
												</Link>
											</div>
										</div>
									</div>
									<div className="w-full">
										<SEOOptimizedImage
											className="w-full object-contain"
											width={1000}
											height={1000}
											quality={100}
											alt={`${node.content.title} Image`}
											src={node.content.image}
										/>
									</div>
								</div>
							</SwiperSlide>
						))}
					</Swiper>

					{/* Custom Navigation Buttons - Bottom Right */}
					<div className="absolute -bottom-12 right-4 z-10 flex gap-6">
						<button
							onClick={goPrev}
							className="flex h-14 w-14 items-center justify-center rounded-full bg-white/70 p-2  hover:bg-white focus:outline-none"
							aria-label="Previous slide"
						>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								className="h-5 w-5 text-[#222]"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
							</svg>
						</button>

						<button
							onClick={goNext}
							className="flex h-14 w-14 items-center justify-center rounded-full bg-white/70 p-2  hover:bg-white focus:outline-none"
							aria-label="Next slide"
						>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								className="h-5 w-5 text-[#222]"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
							</svg>
						</button>
					</div>
				</div>
			</section>
		</>
	);
}

export function HomeMarquee() {
	return (
		<div className="box-border w-full bg-mainPrimaryMedium py-[12px]">
			<Marquee speed={80} gradient={false} pauseOnHover={true}>
				<div style={{ display: "flex", gap: "10px" }}>
					{zzarr.map((item, index) => (
						<img key={index} src={item.img} alt={process.env.NEXT_PUBLIC_COMPANY_NAME} className="h-[50px]" />
					))}
				</div>
			</Marquee>
		</div>
	);
}
