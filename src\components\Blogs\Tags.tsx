'use client'
import React from 'react';
import { useTranslations } from "next-intl";
import type { Blog } from "@/lib/@types/api/blog";
import { Link } from "@/navigation";
interface TagProps {
	tagList: Blog.BlogTag[];
}
const TagsCloud: React.FC<TagProps> = ({ tagList } ) => {
	const t =useTranslations()
	return (
		<div className="filter-tags md:mt-10 mt-6 h-auto overflow-auto">
			<div className="heading6">{t('blog.tags')}</div>
			<div className="list-tags flex items-center flex-wrap gap-3 mt-4">
				{tagList.length>0&&tagList.map((tag) => (
					<div
						key={tag.tag_id}
						className={`tags  bg-white border border-line py-1.5 px-3 rounded-sm  text-secondary cursor-pointer capitalize has-line-before hover:text-black text-secondary2 text-gray-500 `}
					>
						{tag.tag_name}
					</div>
				))}
			</div>
		</div>
	);
};

export default TagsCloud;
