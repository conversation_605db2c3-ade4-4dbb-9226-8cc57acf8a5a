'use client'
import { useTranslations } from "next-intl";
import React from "react";

interface FAQItem {
  id: number;
  question: string;
  answer: string;
}

interface FAQProps {
  backgroundColor?: string;
  title?: string;
  subtitle?: string;
  buttonText?: string;
  faqItems?: FAQItem[];
  onLoginClick?: () => void;
}

const FAQ: React.FC<FAQProps> = ({
  backgroundColor = "bg-blue-600",
  title = "Frequently-Asked-Questions",
  subtitle = "DTC-mode",
  buttonText = "Free-trial",
  faqItems = [
    {
      id: 1,
      question: "Fqa-questions1",
      answer: "Fqa-answer1"
    },
    {
      id: 2,
      question: "Fqa-questions2",
      answer: "Fqa-answer2"
    },
    {
      id: 3,
      question: "Fqa-questions3",
      answer: "Fqa-answer3"
    }
  ],
  onLoginClick
}) => {
  const t = useTranslations("solution");

  return (
    <div className={`${backgroundColor} py-20 max-md:py-12`}>
      <div className="container mx-auto flex gap-20 max-md:flex-col max-md:gap-8 px-4 max-md:px-4">
        {/* 左侧内容 */}
        <div className="flex-1 flex flex-col max-md:order-2 pt-[70px] max-md:pt-5">
          <div className="max-w-md max-md:max-w-full">
            <p className="text-gray-300 text-sm mb-2">{t(subtitle)}</p>
            <h2 className="text-4xl max-md:text-2xl font-bold text-white mb-8 max-md:mb-6 leading-tight">
              {t(title)}
            </h2>
            <button 
              className="bg-white text-gray-800 px-8 max-md:px-6 py-3 max-md:py-2 rounded font-medium hover:bg-gray-100 transition-colors text-base max-md:text-sm"
              onClick={onLoginClick}
            >
              {t(buttonText)}
            </button>
          </div>
        </div>

        {/* 右侧FAQ列表 */}
        <div className="flex-1 max-md:order-1">
          <div className="space-y-0">
            {faqItems.map((item, index) => (
              <div key={item.id} className="group border-b border-gray-400/30 last:border-b-0">
                <div className="flex items-center justify-between py-6 max-md:py-4">
                  <div className="flex items-center gap-6 max-md:gap-3">
                    <span className="text-gray-300 text-sm font-medium w-8 max-md:w-6">
                      {String(item.id).padStart(2, '0')}
                    </span>
                    <span className="text-white text-lg max-md:text-base font-medium">
                      {t(item.question)}
                    </span>
                  </div>
                  <div className="text-white">
                    {/* 默认显示加号，hover时显示减号 */}
                    <div className="group-hover:hidden transition-all duration-300 ease-in-out">
                      <svg className="w-6 h-6 max-md:w-5 max-md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                    </div>
                    <div className="hidden group-hover:block transition-all duration-500 ease-in-out">
                      <svg className="w-6 h-6 max-md:w-5 max-md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                      </svg>
                    </div>
                  </div>
                </div>
                {/* hover时显示答案 - 添加动画效果 */}
                <div className="overflow-hidden transition-all duration-500 ease-in-out max-h-0 group-hover:max-h-96 opacity-0 group-hover:opacity-100">
                  <div className="pb-6 max-md:pb-4 pl-14 max-md:pl-10 pr-6 max-md:pr-4">
                    <p className="text-white/90 leading-relaxed text-base max-md:text-sm">
                      {t(item.answer)}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FAQ; 