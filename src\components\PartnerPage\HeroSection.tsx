import PartnerImg from "./PartnerImg";

export default function HeroSection() {
	const partnerPartners = [
		{
			name: "Google",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_partners_1.png",
		},
		{
			name: "<PERSON><PERSON>",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_partners_2.png",
		},
		{
			name: "AliExpress",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_partners_3.png",
		},
		{
			name: "PayPal",
			logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_partners_4.png",
		},
		{ name: "A<PERSON>", logo: "https://www.shoplazza.cn/wstmart/home/<USER>/default/images/inner_partners_5.png" },
	];
	return (
		<>
			<section className="bg-[#f8f4ec] py-16">
				<div className=" container h-[60vh] px-4 sm:px-6 lg:px-8 max-lg:!h-[30vh]">
					{/* Main Title and Buttons */}
					<div className="mb-16 text-center">
						<h1 className="mb-6 text-4xl font-bold text-gray-900 md:text-5xl">我们的合作伙伴</h1>
						<p className="mx-auto mb-8 max-w-2xl text-lg text-gray-600">
							来加入合作伙伴中心，成为我们的合作伙伴，开拓您的商业版图
						</p>
						<div className="flex flex-col justify-center gap-4 sm:flex-row">
							<button className="rounded bg-gray-900 px-8 py-3 text-sm font-medium text-white hover:bg-gray-800">
								立即加入
							</button>
							<button className="rounded border border-gray-300 px-8 py-3 text-sm font-medium text-gray-700 hover:border-gray-400">
								寻找合作伙伴
							</button>
						</div>
					</div>
				</div>
			</section>

			<div className="bg-[#5959ff]">
				<div className="container flex h-96 flex-col items-end justify-end max-lg:h-48 max-md:h-auto  max-md:py-6">
					{/* Hero Image */}
					<div className="relative  rounded-lg">
						<img
							src="https://www.shoplazza.cn/wstmart/home/<USER>/default/images/partners_block.jpg"
							alt="合作伙伴工作场景"
							className="h-auto w-full object-cover"
						/>
					</div>
				</div>
			</div>

			<section className="bg-[#5959ff] py-[100px] text-white max-md:py-6">
				<div className="container px-4 sm:px-6 lg:px-8">
					{/* Left Content */}
					<div className="grid grid-cols-1 items-center gap-12 ">
						<div className="max-w-[800px]">
							<h2 className="mb-6 text-3xl font-bold md:text-4xl">战略合作伙伴</h2>
							<p className="mb-8 text-lg leading-relaxed opacity-90">
								作为一家拥有强力合作伙伴的行业领先企业，我们与业界最好的团队合作，致力于为客户提供最佳电子商务体验。让商家轻松上手开启广阔前景。
							</p>
							<button className="rounded border  bg-white px-6 py-3 text-sm font-medium text-[#5959ff] backdrop-blur-sm hover:bg-opacity-30">
								立即注册
							</button>
						</div>

						{/* Partner Logos */}
						<div className="grid grid-cols-3  max-md:grid-cols-2">
							{partnerPartners.map((partner, index) => (
								<div
									key={index}
									className="flex h-[175px] max-md:h-auto items-center justify-center  p-6 max-md:p-0 backdrop-blur-sm transition-all duration-300 "
								>
									<img src={partner.logo} alt={partner.name} className="h-full  object-contain" />
								</div>
							))}
						</div>
					</div>
				</div>
			</section>


		</>
	);
}
