'use client'
import React, { useEffect, useRef, useState } from "react";
import { useTranslations } from "next-intl";
import clsx from "clsx";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Pagination, Navigation } from "swiper/modules";
import { Link } from "@/navigation";
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";

interface ReviewData {
  id: number;
  name: string;
  Industry: string;
  WebsiteType: string;
  KeyFeatures: string;
  Solution: string;
  Description: string;
  websiteImage: string;
}

interface CustomerReviewsProps {
  backgroundColor?: string;
  mainTitle?: string;
  reviewsData?: ReviewData[];
}

const CustomerReviews: React.FC<CustomerReviewsProps> = ({
  backgroundColor = "bg-[#f2eada]",
  mainTitle = "Customer-reviews",
  reviewsData = [
    {
      id: 1,
      name: "Customer1",
      Industry: "Customer1-Industry",
      WebsiteType: "Customer1-WebsiteType",
      KeyFeatures: "Customer1-KeyFeatures",
      Solution: "Customer1-Solution",
      Description: "Customer1-Description",
      websiteImage: "/image/solution/review1.webp"
    },
    {
      id: 2,
      name: "Customer2",
      Industry: "Customer2-Industry",
      WebsiteType: "Customer2-WebsiteType",
      KeyFeatures: "Customer2-KeyFeatures",
      Solution: "Customer2-Solution",
      Description: "Customer2-Description",
      websiteImage: "/image/solution/review2.webp"
    },
    {
      id: 3,
      name: "Customer3",
      Industry: "Customer3-Industry",
      WebsiteType: "Customer3-WebsiteType",
      KeyFeatures: "Customer3-KeyFeatures",
      Solution: "Customer3-Solution",
      Description: "Customer3-Description",
      websiteImage: "/image/solution/review3.webp"
    }
  ]
}) => {
  const t = useTranslations('solution');
	const [activeCollectionSlug, setActiveCollectionSlug] = useState(reviewsData[0].id);
	const [underlineStyle, setUnderlineStyle] = useState({ left: 0, width: 0 });
	const [swiperInstance, setSwiperInstance] = useState<any>(null);
	const tabsRef = useRef<(HTMLButtonElement | null)[]>([]);
	const textRefs = useRef<(HTMLSpanElement | null)[]>([]);

	useEffect(() => {
		const activeIndex = reviewsData.findIndex(({ id }) => id === activeCollectionSlug);

		if (activeIndex !== -1 && tabsRef.current[activeIndex]) {
			const activeTab = tabsRef.current[activeIndex]!;
			setUnderlineStyle({
				left: activeTab.offsetLeft,
				width: activeTab.offsetWidth,
			});

			if (swiperInstance) {
				swiperInstance.slideTo(activeIndex);
			}

			if (window.innerWidth < 768) {
				activeTab.scrollIntoView({
					behavior: "smooth",
					block: "nearest",
					inline: "center",
				});
			}
		}
	}, [activeCollectionSlug, swiperInstance]);

	const handleSwiperInit = (swiper: any) => {
		setSwiperInstance(swiper);
	};

	const handleSlideChange = (swiper: any) => {
		const newIndex = swiper.activeIndex;
		if (reviewsData[newIndex]) {
			setActiveCollectionSlug(reviewsData[newIndex].id);
		}
	};
	const goNext = () => {
		if (swiperInstance) {
			swiperInstance.slideNext();
		}
	};

	const goPrev = () => {
		if (swiperInstance) {
			swiperInstance.slidePrev();
		}
	};

  return (
    <div className={`${backgroundColor} py-[60px] md:py-[120px]`}>
        {/* 主标题 */}
        <h2 className="text-center text-[36px] font-bold leading-tight text-titleLight max-lg:text-[24px] mb-10">
          {t(mainTitle)}
        </h2>

        <div className="container relative mb-8 flex flex-nowrap items-center justify-start  gap-8 overflow-x-auto border-b border-titleLight/30 [-ms-overflow-style:none] [scrollbar-width:none] md:mb-12 [&::-webkit-scrollbar]:hidden">
					{reviewsData.map(({ id, name }, index) => (
						<button
							ref={(el) => (tabsRef.current[index] = el)}
							key={id}
							onClick={() => setActiveCollectionSlug(id)}
							className={clsx(
								"flex-1 whitespace-nowrap px-2 py-3 pb-6 text-sm uppercase tracking-wider text-titleLight transition-colors duration-300 md:px-4",
								{
									"font-bold": activeCollectionSlug === id,
									"font-medium": activeCollectionSlug !== id,
								},
							)}
						>
							<span
								ref={(el) => (textRefs.current[index] = el)}
								className="inline-block text-[22px] font-semibold max-md:text-[18px]"
							>
								{t(name)}
							</span>
						</button>
					))}
					<div
						className="absolute bottom-[-1px] h-[5px] bg-mainPrimary transition-all duration-300 ease-in-out"
						style={{ left: underlineStyle.left, width: underlineStyle.width }}
					/>
				</div>
        
				{/* Swiper Carousel */}
				<div className="container relative">
					<Swiper
						onSwiper={handleSwiperInit}
						onSlideChange={handleSlideChange}
						initialSlide={0}
						spaceBetween={50}
						slidesPerView={1}
						loop
						modules={[Pagination, Navigation, Autoplay]}
						// autoplay={{ delay: 5000, disableOnInteraction: false }}
						className="review-swiper"
					>
						{reviewsData.map(({ id, name, Industry, WebsiteType, KeyFeatures, Solution, Description, websiteImage }) => (
							<SwiperSlide key={id}>
								<div className="grid grid-cols-2 gap-10 max-lg:grid-cols-1">
									<div className="flex flex-col justify-center pr-40 max-lg:pr-0">
										<div>
											<h3 className="text-3xl max-md:text-xl font-bold text-[#222222] mb-3 max-md:mb-4">
											{t(name)}
											</h3>
											<p className="text-base max-md:text-base text-[#525050] mb-8 max-md:mb-6 leading-relaxed">
											{t(Industry)}
											</p>
											<p className="text-lg max-md:text-base text-[#222222] mb-8 max-md:mb-6 leading-relaxed">
												{t(WebsiteType)}
											</p>
											<p className="text-lg max-md:text-base text-[#222222] mb-8 max-md:mb-6 leading-relaxed">
												{t(KeyFeatures)}
											</p>
											<p className="text-lg max-md:text-base text-[#222222] mb-8 max-md:mb-6 leading-relaxed font-bold">
												{t(Solution)}
											</p>
											<p className="text-lg leading-relaxed text-titleLight max-md:text-base">
												{t(Description)}
											</p>
											<div className="my-10 text-right text-sm font-medium text-[#222222] max-md:text-xs">
												— {t(name)}
											</div>
											<div className="group">
												<Link
													href="/"
													className="relative inline-block text-[18px] text-titleLight hover:text-[#555555]"
												>
													{t('Learn-more')}
													<span className="absolute bottom-0 left-0 h-[1px] w-0 bg-[#555555] transition-all duration-300 group-hover:w-full"></span>
													<i className="ri-arrow-right-s-line text-2xl duration-300"></i>
												</Link>
											</div>
										</div>
									</div>
									<div className="w-full">
										<SEOOptimizedImage
											className="w-full object-contain"
											width={1000}
											height={1000}
											quality={100}
											alt={`${name} Image`}
											src={websiteImage}
										/>
									</div>
								</div>
							</SwiperSlide>
						))}
					</Swiper>
          {/* Custom Navigation Buttons - Bottom Right */}
					<div className="absolute -bottom-12 right-4 z-10 flex gap-6">
						<button
							onClick={goPrev}
							className="flex h-14 w-14 items-center justify-center rounded-full bg-white/70 p-2  hover:bg-white focus:outline-none"
							aria-label="Previous slide"
						>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								className="h-5 w-5 text-[#222]"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
							</svg>
						</button>

						<button
							onClick={goNext}
							className="flex h-14 w-14 items-center justify-center rounded-full bg-white/70 p-2  hover:bg-white focus:outline-none"
							aria-label="Next slide"
						>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								className="h-5 w-5 text-[#222]"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
							</svg>
						</button>
					</div>
				</div>
		</div>
	);
};

export default CustomerReviews;