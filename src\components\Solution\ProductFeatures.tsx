'use client'
import { useTranslations } from "next-intl";
import React, { useState } from "react";

interface FeatureCard {
  id: number;
  icon: string;
  title: string;
  description: string;
}

interface TabData {
  id: string;
  name: string;
  features: FeatureCard[];
}

interface ProductFeaturesProps {
  backgroundColor?: string;
  mainTitle?: string;
  mainTitleDesc1?: string;
  mainTitleDesc2?: string;
  buttonText?: string;
  buttonOnClick?: () => void;
  tabsData?: TabData[];
}

const ProductFeatures: React.FC<ProductFeaturesProps> = ({
  backgroundColor = "bg-white",
  mainTitle = "Product-features",
  mainTitleDesc1 = "Product-features-Desc1",
  mainTitleDesc2 = "Product-features-Desc2",
  buttonText = "Register-for-a-trial-now",
  buttonOnClick,
  tabsData = [
    {
      id: "sales",
      name: "Shop-sales",
      features: [
        {
          id: 1,
          icon: "/image/solution/1.svg",
          title: "Shop-sales-title1",
          description: "Shop-sales-title1-desc"
        },
        {
          id: 2,
          icon: "/image/solution/2.svg",
          title: "Shop-sales-title-2",
          description: "Shop-sales-title2-desc"
        },
        {
          id: 3,
          icon: "/image/solution/3.svg",
          title: "Shop-sales-title-3",
          description: "Shop-sales-title3-desc"
        },
        {
          id: 4,
          icon: "/image/solution/4.svg",
          title: "Shop-sales-title-4",
          description: "Shop-sales-title4-desc"
        },
        {
          id: 5,
          icon: "/image/solution/5.svg",
          title: "Shop-sales-title-5",
          description: "Shop-sales-title5-desc"
        }
      ]
    },
    {
      id: "marketing",
      name: "Store-marketing",
      features: [
        {
          id: 1,
          icon: "/image/solution/6.svg",
          title: "Store-marketing-title1",
          description: "Store-marketing-title1-desc"
        },
        {
          id: 2,
          icon: "/image/solution/7.svg",
          title: "Store-marketing-title2",
          description: "Store-marketing-title2-desc"
        },
        {
          id: 3,
          icon: "/image/solution/8.svg",
          title: "Store-marketing-title3",
          description: "Store-marketing-title3-desc"
        },
        {
          id: 4,
          icon: "/image/solution/9.svg",
          title: "Store-marketing-title4",
          description: "Store-marketing-title4-desc"
        },
        {
          id: 5,
          icon: "/image/solution/10.svg",
          title: "Store-marketing-title5",
          description: "Store-marketing-title5-desc"
        }
      ]
    },
    {
      id: "management",
      name: "Store-management",
      features: [
        {
          id: 1,
          icon: "/image/solution/11.svg",
          title: "Store-management-title1",
          description: "Store-management-title1-desc"
        },
        {
          id: 2,
          icon: "/image/solution/12.svg",
          title: "Store-management-title2",
          description: "Store-management-title2-desc"
        },
        {
          id: 3,
          icon: "/image/solution/13.svg",
          title: "Store-management-title3",
          description: "Store-management-title3-desc"
        }
      ]
    }
  ]
}) => {
  const t=useTranslations("solution");
  const [activeTab, setActiveTab] = useState("sales");
  const currentTab = tabsData.find(tab => tab.id === activeTab) || tabsData[0];

  return (
    <div className={`${backgroundColor} py-20 max-md:py-12`}>
      <div className="container mx-auto px-4 max-md:px-4">
        {/* 主标题和按钮 */}
        <div className="flex justify-between items-center mb-12 max-md:flex-col max-md:gap-6 max-md:mb-8">
          <div className="text-4xl max-md:text-2xl font-bold text-[#222222] max-md:text-center">
            {t(mainTitle)}
          <p className="text-lg max-md:text-base text-[#666666] max-md:text-center pt-4 font-medium">{t(mainTitleDesc1)}</p>
          <p className=" text-lg max-md:text-base text-[#666666] max-md:text-center pt-2 font-medium">{t(mainTitleDesc2)}</p>
          </div>
          <button
            onClick={buttonOnClick}
            className="px-6 max-md:px-4 py-3 max-md:py-2 border border-[#222222] bg-white text-[#222222] font-medium hover:bg-[#222222] hover:text-white transition-colors text-base max-md:text-sm"
          >
            {t(buttonText)}
          </button>
        </div>

        {/* Tab导航 */}
        <div className="flex justify-center mb-12 w-full max-md:mb-8">
          <div className="flex w-full border-b border-gray-300 overflow-x-auto max-md:overflow-x-scroll">
            {tabsData.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 max-md:flex-none max-md:min-w-max pb-4 px-2 max-md:px-4 text-lg max-md:text-sm font-medium transition-all duration-300 ${
                  activeTab === tab.id
                    ? 'text-black border-b-2 border-red-500 font-bold'
                    : 'text-black hover:text-gray-800 font-bold'
                }`}
              >
                {t(tab.name)}
              </button>
            ))}
          </div>
        </div>

        {/* 功能卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-md:gap-4">
          {currentTab.features.map((feature) => (
            <div
              key={feature.id}
              className="bg-white rounded-lg p-6 max-md:p-4 hover:shadow-lg border border-[#e8e8e8]"
            >
              <div className="text-3xl max-md:text-2xl mb-4">
                <img src={feature.icon} alt={feature.title} className="w-5 h-5 max-md:w-10 max-md:h-10" />
              </div>
              <h3 className="text-lg font-bold text-[#222222] mb-3 max-md:mb-2">
                {t(feature.title)}
              </h3>
              <p className="text-gray-500 leading-relaxed max-md:text-sm">
                {t(feature.description)}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProductFeatures; 