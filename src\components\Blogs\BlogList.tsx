"use client";
import React from "react";
import { type Blog } from "@/lib/@types/api/blog";
import BlogItemDefault from "@/components/Blogs/BlogItemDefault";
import clsx from "clsx";
import MyEmpty from "../MyEmpty";
import { useTranslations } from "next-intl";
interface BlogListProps {
	blogList: Blog.BlogListItem[];
}

const BlogList: React.FC<BlogListProps> = ({ blogList }) => {
	const t = useTranslations();
	return blogList.length > 0 ? (
		<div className="list-blog grid grid-cols-1 gap-x-4 gap-y-6 md:gap-x-6 md:gap-y-8">
			{blogList.map((item, index) => {
				return (
					<div key={index} className="w-full">
						<BlogItemDefault news={item} index={index} />
					</div>
				);
			})}
		</div>
	) : (
		<div className="flex h-full w-full items-center justify-center">
			<MyEmpty
				link={"/blog"}
				text={""}
				description={t("nav.Noblog")}
				bottomText={t("nav.Return_to_blog")}
				className="py-20 max-md:py-4"
			>
				<i className="ri-draft-line text-4xl  !text-ddd"></i>
			</MyEmpty>
		</div>
	);
};

export default BlogList;
