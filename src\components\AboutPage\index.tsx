"use client";
import React from "react";
import "react-image-lightbox/style.css";
import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import GetToKnowUs from "@/components/AboutPage/GetToKnowUs";
import StatsSection from "@/components/AboutPage/StatsSection";
import OfficeSection from "@/components/AboutPage/OfficeSection";
import VisionMission from "@/components/AboutPage/VisionMission";
import CompanyValues from "@/components/AboutPage/CompanyValues";
import DevelopmentJourney from "@/components/AboutPage/DevelopmentJourney";
import VentureCapital from "@/components/AboutPage/VentureCapital";
import HonorSection from '@/components/AboutPage/OurHonor';
import ProductFunction from '@/components/AboutPage/ProductFunction';
import ContactUs from '@/components/AboutPage/ContactUs';
import EcologicalPartner from '@/components/AboutPage/EcologicalPartner';
import LatestUpdates from '@/components/AboutPage/LatestUpdates';
import RegisterNowButton from '@/components/AboutPage/RegisterNowButton';
function Index() {
  const t = useTranslations("nav");


  return (
    <main className="md:pt-[134px] pt-[64px]">
      <GetToKnowUs />
      <StatsSection />
      <OfficeSection />
      <VisionMission />
      <CompanyValues />
      <DevelopmentJourney />
      <VentureCapital />
      <HonorSection />
      <ProductFunction />
      <ContactUs />
      <EcologicalPartner />
      <LatestUpdates />
      <RegisterNowButton />
    </main>
  );
}

export default React.memo(Index);
