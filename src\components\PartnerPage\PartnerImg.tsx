import React from "react";

export default function PartnerImg({title,description,data}) {

	return (
		<div className="bg-white py-20 max-md:py-0">
			<div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="w-full h-[1px] bg-[#e5e7eb] mb-20"></div>
				{/* 标题部分 */}
				<div className="mb-12 text-left">
					<h2 className="mb-4 text-2xl font-bold text-gray-900">{title}</h2>
					<p className="text-lg text-gray-600">
						{description}
					</p>
				</div>

				{/* 合作伙伴网格 */}
				<div className="grid  gap-8 grid-cols-4 max-lg:grid-cols-3 max-md:grid-cols-2">
					{data.map((partner) => (
						<div
							key={partner.id}
							className="group overflow-hidden border  transition-all duration-300 "
						>
							{/* 图片部分 */}
							<div className="h-[160px] max-md:h-auto ">
								<img
									src={partner.logo}
									alt={partner.name}
									className="h-full object-contain  transition-all duration-300"
								/>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
}
