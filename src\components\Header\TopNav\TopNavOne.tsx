"use client";

import React, { useEffect, useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { usePathname } from "next/navigation";
import { Link } from "@/navigation";
import Image from "next/image";
import Search from "../Menu/search";
import { useRouter } from "@/navigation";
import NavUser from "@/components/User/nav-user";
import { useShoppingCartStore } from "@/lib/store/shoppingCart";
import CountBadge from "@/components/Badge/count-badge";
import CartDrawer from "@/components/Product/ShoppingCart/cartDrawer";
import { useShoppingCart } from "@/lib/hooks/useShoppingCart";
import { getInquiry } from "@/lib/utils/util";
import { useLoveStore } from "@/lib/store/love.store";
import { menus } from "@/lib/menu";
import clsx from "clsx";
import useMenuMobile from "@/store/useMenuMobile";
import { contactInfo } from "@/lib/contacts";
import Translate from "@/components/Translate";
import Svg from "../Menu/Svg";
import { useCompareStore } from "@/lib/store/Compare.store";
import LoginModal from "@/components/User/login-modal";
interface Props {
	props: string;
	slogan?: string;
}

const TopNavOne: React.FC<Props> = () => {
	const { findCheckout } = useShoppingCart();
	const t = useTranslations();

	const pathname = usePathname(); // 当前路径，例如 /en/blog/123
	const [currentSlug, setCurrentSlug] = useState("");
	const { openMenuMobile, handleMenuMobile } = useMenuMobile();

	// 登录模态框状态管理
	const [openLoginModal, setOpenLoginModal] = useState(false);
	const [activeId, setActiveId] = useState(1); // 1: 登录, 2: 注册

	// 处理登录按钮点击
	const handleLoginClick = () => {
		setActiveId(1); // 设置为登录面板
		setOpenLoginModal(true);
		// 只有在移动端菜单打开时才关闭它
		if (openMenuMobile) {
			handleMenuMobile();
		}
	};

	// 处理注册按钮点击
	const handleRegisterClick = () => {
		setActiveId(2); // 设置为注册面板
		setOpenLoginModal(true);
		// 只有在移动端菜单打开时才关闭它
		if (openMenuMobile) {
			handleMenuMobile();
		}
	};

	//喜欢
	const [loveCount, setLoveCount] = useState(0);
	const { loveIds } = useLoveStore();
	//对比
	const [CompareCount, setCompareCount] = useState(0);
	let { compareIds } = useCompareStore();
	// 购物车
	const { cartList } = useShoppingCartStore();
	const [count, setCount] = useState(0);
	const [Inquirycount, setInquirycount] = useState(0);
	// 顶部通知栏状态
	const [isBannerVisible, setIsBannerVisible] = useState(true);
	// 滚动状态
	const [isScrolled, setIsScrolled] = useState(false);

	useEffect(() => {
		// 1. 刷新购物车数据
		findCheckout("default-channel");

		const handleResize = () => {
			let Inquirydata = getInquiry();
			setInquirycount(Inquirydata.length);
		};
		handleResize();
		window.addEventListener("storage", handleResize);
		// Cleanup function to remove event listener
		return () => {
			window.removeEventListener("storage", handleResize);
		};
	}, [currentSlug]);

	let router = useRouter();

	useEffect(() => {
		setCount(cartList?.lines?.length || 0);
	}, [cartList]);
	useEffect(() => {
		console.log(pathname);
		setCurrentSlug(pathname);
	}, [pathname]);

	useEffect(() => {
		setLoveCount(() => {
			return loveIds.length > 99 ? 99 : loveIds.length;
		});
		setCompareCount(() => {
			return compareIds.length > 99 ? 99 : compareIds.length;
		});
	}, [loveIds, compareIds]);

	// 滚动监听
	useEffect(() => {
		const handleScroll = () => {
			const scrollTop = window.scrollY;
			// 滚动超过100px时显示白色背景和阴影
			setIsScrolled(scrollTop > 40);
		};

		window.addEventListener("scroll", handleScroll);
		return () => {
			window.removeEventListener("scroll", handleScroll);
		};
	}, []);

	const GoToPage = (url: string) => {
		router.push(url);
		handleMenuMobile();
	};

	// 为菜单添加二级菜单数据（写死的演示数据）
	const menusWithChildren = menus.map((menu) => {
		if (menu.id === 20) {
			// Products 菜单
			return {
				...menu,
				children: [
					{
						id: 201,
            name: "menu.t12",
						link: "/products/hot",
						hasSlug: true,
						show: true,
						description: "Featured bestselling products",
						icon: "ri-fire-line",
					},
					{
						id: 202,
            name: "menu.t11",
						link: "/products/new",
						hasSlug: true,
						show: true,
						description: "Latest product releases",
						icon: "ri-star-line",
					},
					{
						id: 203,
            name: "menu.t10",
						link: "/products/sale",
						hasSlug: true,
						show: true,
						description: "Limited time special offers",
						icon: "ri-price-tag-3-line",
					},
					{
						id: 204,
            name: "menu.t9",
						link: "/products/brands",
						hasSlug: true,
						show: true,
						description: "Premium brand collections",
						icon: "ri-award-line",
					},
				],
			};
		}
		if (menu.id === 3) {
			// Blog 菜单
			return {
				...menu,
				children: [
					{
						id: 301,
            name: "menu.t8",
						link: "/blog/industry",
						hasSlug: true,
						show: true,
						description: "最新行业动态资讯",
						icon: "ri-newspaper-line",
					},
					{
						id: 302,
            name: "menu.t7",
						link: "/blog/tutorials",
						hasSlug: true,
						show: true,
						description: "详细操作指导教程",
						icon: "ri-book-open-line",
					},
					{
						id: 303,
            name: "menu.t6",
						link: "/blog/cases",
						hasSlug: true,
						show: true,
						description: "成功客户案例分享",
						icon: "ri-lightbulb-line",
					},
					{
						id: 304,
						name: "menu.t1",
						link: "/blog/docs",
						hasSlug: true,
						show: true,
						description: "开发者技术文档",
						icon: "ri-code-line",
					},
				],
			};
		}
		if (menu.id === 4) {
			// About Us 菜单
			return {
				...menu,
				children: [
					{
						id: 401,
            name: "menu.t5",
						link: "/about-us/company",
						hasSlug: true,
						show: true,
						description: "企业发展历程介绍",
						icon: "ri-building-line",
					},
					{
						id: 402,
            name: "menu.t4",
						link: "/about-us/team",
						hasSlug: true,
						show: true,
						description: "核心团队成员介绍",
						icon: "ri-team-line",
					},
					{
						id: 403,
            name: "menu.t3",
						link: "/about-us/history",
						hasSlug: true,
						show: true,
						description: "公司发展重要里程碑",
						icon: "ri-time-line",
					},
					{
						id: 404,
						name: "menu.t2",
						link: "/about-us/culture",
						hasSlug: true,
						show: true,
						description: "企业价值观和文化",
						icon: "ri-heart-line",
					},
				],
			};
		}
		return menu;
	});

	return (
		<>
			<div className="top-nav">
				<TopBanner onVisibilityChange={setIsBannerVisible} />
				<MainHeader
					handleMenuMobile={handleMenuMobile}
					t={t}
					loveCount={loveCount}
					CompareCount={CompareCount}
					Inquirycount={Inquirycount}
					count={count}
					handleLoginClick={handleLoginClick}
					handleRegisterClick={handleRegisterClick}
					menus={menusWithChildren}
					isBannerVisible={isBannerVisible}
					isScrolled={isScrolled}
				/>
				<MobileMenu
					openMenuMobile={openMenuMobile}
					handleMenuMobile={handleMenuMobile}
					GoToPage={GoToPage}
					t={t}
					onLoginClick={handleLoginClick}
					onRegisterClick={handleRegisterClick}
					menus={menusWithChildren}
				/>
			</div>

			{/* 登录模态框 */}
			<LoginModal
				openModal={openLoginModal}
				setOpenModal={setOpenLoginModal}
				activeId={activeId}
				setActiveId={setActiveId}
			/>
		</>
	);
};

export default TopNavOne;

// 顶部通知栏组件
function TopBanner({ onVisibilityChange }: { onVisibilityChange?: (isVisible: boolean) => void }) {
	const [isHidden, setIsHidden] = useState(false);
	const t = useTranslations("nav");

	// 初始化时触发事件
	useEffect(() => {
		window.dispatchEvent(
			new CustomEvent("bannerVisibilityChange", {
				detail: { isVisible: !isHidden },
			}),
		);
	}, [isHidden]);

	const handleClose = () => {
		setIsHidden(true);
		onVisibilityChange?.(false);

		// 触发全局事件通知 banner 状态变化
		window.dispatchEvent(
			new CustomEvent("bannerVisibilityChange", {
				detail: { isVisible: false },
			}),
		);
	};

	if (isHidden) return null;

	return (
		<div className="h-[64px] w-full bg-mainPrimaryMedium text-sm text-titleLight max-md:hidden">
			<div className="relative mx-auto flex h-full items-center justify-center px-[30px]">
				<div className="text-center">
					<span className="text-sm">
						《品店 独立站 0-1 搭建指南》从独立站认知到建站实操，助您轻松开启全球业务！
						<Link href={"/"} className="hover:!text-underline text-white hover:text-main">
							→ 点击领取
						</Link>
					</span>
				</div>

				<div className="absolute right-[30px] flex items-center space-x-3">
					<button
						onClick={handleClose}
						className="text-3xl text-white transition-colors hover:text-orange-200"
						aria-label="关闭通知栏"
					>
						×
					</button>
				</div>
			</div>
		</div>
	);
}

// 主导航栏组件
function MainHeader({
	handleMenuMobile,
	t,
	loveCount,
	CompareCount,
	Inquirycount,
	count,
	handleLoginClick,
	handleRegisterClick,
	menus,
	isBannerVisible,
	isScrolled,
}: {
	handleMenuMobile: () => void;
	t: any;
	loveCount: number;
	CompareCount: number;
	Inquirycount: number;
	count: number;
	handleLoginClick: () => void;
	handleRegisterClick: () => void;
	menus: any[];
	isBannerVisible: boolean;
	isScrolled: boolean;
}) {
	return (
		<div
			className={`w-full transition-all duration-300 hover:bg-white ${
				isScrolled ? "bg-white shadow-lg" : "bg-transparent"
			}`}
		>
			<div className="px-[30px] max-md:px-[24px]">
				<div className="flex h-[70px] items-center justify-between max-md:h-[64px]">
					{/* 左侧：Logo + 桌面端导航 */}
					<div className="flex items-center space-x-8">
						<Link href="/" className="flex items-center">
							<Image
								src="/image/logo.webp"
								width={1000}
								height={1000}
								quality={100}
								priority={true}
								unoptimized={true}
								alt="Pinshop Saleor Logo"
								className="w-[150px] h-full object-contain max-md:w-[120px]"
							/>
						</Link>

						{/* 桌面端导航菜单 */}
						<nav className="hidden items-center xl:flex">
							{menus.map(
								(item, index) =>
									item.show && (
										<div key={item.id} className="group relative flex items-center">
											{/* 分割线 */}
											{index > 0 && <div className="mx-4 h-4 w-px bg-gray-300"></div>}
											{/* 一级菜单项 */}
											<Link
												href={item.link}
												className="flex items-center py-2 text-[16px] font-medium uppercase text-titleLight transition-colors duration-200 hover:text-black"
											>
												{t(item.name)}
												{/* 如果有子菜单，显示加减号 */}
												{/* {item.children && item.children.length > 0 && (
													<div className="ml-1 text-[16px] transition-all duration-200">
														<i className="ri-add-line text-black group-hover:hidden"></i>
														<i className="ri-subtract-line hidden text-black group-hover:inline"></i>
													</div>
												)} */}
												{/* <span className="absolute bottom-0 left-0 h-0.5 w-0 bg-red-500 transition-all duration-200 group-hover:w-full"></span> */}
											</Link>

											{/* 二级菜单下拉框 - 铺满版心容器，相对于菜单项居中 */}
											{/* {item.children && item.children.length > 0 && (
												<div
													className="container invisible fixed left-0 right-0 z-50 border-t border-gray-100 bg-white opacity-0 shadow-lg transition-all duration-300 group-hover:visible group-hover:opacity-100"
													style={{
														top: isBannerVisible ? "118px" : "70px",
													}}
												>
													<div className="px-[30px] py-6">
														<div className="grid grid-cols-3 gap-8">
															{item.children &&
																item.children.slice(0, 3).map((child: any, index: number) => (
																	<div key={child.id} className="group">
																		<Link href={child.link} className="block">
																			<div className="flex items-start space-x-3">
																				<div className="flex-1 rounded-md p-[16px] hover:bg-[#f6f7f7]">
																					<div className="mb-2 flex items-center space-x-2">
																						<h4 className="text-[16px] font-medium text-titleLight transition-colors duration-200">
																							{t(child.name)}
																						</h4>
																						{index === 0 && (
																							<span className="rounded-full bg-red-500 px-2 py-1 text-xs font-medium text-white">
																								HOT
																							</span>
																						)}
																					</div>
																					<p className="text-[14px] leading-relaxed text-descColor">
																						{child.description}
																					</p>
																				</div>
																			</div>
																		</Link>
																	</div>
																))}
														</div>
													</div>
												</div>
											)} */}
										</div>
									),
							)}
						</nav>
					</div>
					{/* 右侧：功能按钮 */}
					<div className="flex items-center space-x-3">
						{/* 搜索按钮 */}
						{/* <Search isVisible={true} /> */}

						{/* 桌面端功能按钮 */}
						<div className="hidden items-center space-x-2 xl:flex">
							{/* 用户 */}
							{/* <NavUser>
								<div className="p-2 text-gray-600 hover:text-red-500 transition-colors cursor-pointer">
									<i className="ri-user-line text-lg"></i>
								</div>
							</NavUser> */}

							{/* 对比 */}
							{/* <Link
								href="/compare"
								className="p-2 text-gray-600 hover:text-red-500 transition-colors relative"
								aria-label="对比"
							>
								<Svg isScrolled={true} ishead={true} />
								{!!CompareCount && (
									<span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
										{CompareCount}
									</span>
								)}
							</Link> */}

							{/* 收藏 */}
							{/* <Link
								href="/collection"
								className="p-2 text-gray-600 hover:text-red-500 transition-colors relative"
								aria-label="收藏"
							>
								<i className="ri-heart-3-line text-lg"></i>
								{!!loveCount && (
									<span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
										{loveCount}
									</span>
								)}
							</Link> */}

							{/* 询盘 */}
							{/* <Link
								href="/inquiry"
								className="p-2 text-gray-600 hover:text-red-500 transition-colors relative"
								aria-label="询盘"
							>
								<i className="ri-file-list-line text-lg"></i>
								<CountBadge count={Inquirycount} />
							</Link> */}

							{/* 购物车 */}
							{/* {process.env.NEXT_PUBLIC_SITE_TYPE == "toc" && (
								<CartDrawer>
									<div className="p-2 text-gray-600 hover:text-red-500 transition-colors relative cursor-pointer">
										<i className="ri-shopping-bag-line text-lg"></i>
										<CountBadge count={count} />
									</div>
								</CartDrawer>
							)} */}
						</div>

						{/* 登录注册按钮 */}
						<div className="ml-4 hidden items-center space-x-3 xl:flex">
							<button
								onClick={handleLoginClick}
								className="px-3 py-1.5 text-[16px] text-titleLight transition-colors hover:text-red-500"
							>
								登录
							</button>
							<button onClick={handleRegisterClick} className={`px-[24px] py-[11px] text-[16px] bg-mainPrimary text-white hover:bg-opacity-80 rounded`}>
								免费试用
							</button>
						</div>

						{/* 移动端菜单按钮 */}
						<button
							onClick={handleMenuMobile}
							className="flex h-10 w-10 items-center justify-center rounded-md text-gray-700 transition-colors hover:bg-gray-100 hover:text-red-500 xl:hidden"
							aria-label="打开菜单"
						>
							<i className="ri-menu-line text-2xl"></i>
						</button>
					</div>
				</div>
			</div>
		</div>
	);
}
// 移动端菜单组件
function MobileMenu({
	openMenuMobile,
	handleMenuMobile,
	GoToPage,
	t,
	onLoginClick,
	onRegisterClick,
	menus,
}: {
	openMenuMobile: boolean;
	handleMenuMobile: () => void;
	GoToPage: (url: string) => void;
	t: any;
	onLoginClick: () => void;
	onRegisterClick: () => void;
	menus: any[];
}) {
	const [expandedItems, setExpandedItems] = useState<string[]>([]);

	const toggleExpanded = (itemId: string) => {
		setExpandedItems((prev) =>
			prev.includes(itemId) ? prev.filter((id) => id !== itemId) : [...prev, itemId],
		);
	};

	return (
		<>
			{/* 遮罩层 */}
			{openMenuMobile && (
				<div
					className="fixed inset-0 z-40 bg-black bg-opacity-50 transition-opacity duration-300"
					onClick={handleMenuMobile}
				></div>
			)}

			{/* 菜单内容 */}
			<div
				id="mobile-menu-new"
				className={`fixed left-0 top-0 z-50 h-full w-full bg-white transition-transform duration-300 ease-in-out ${
					openMenuMobile ? "translate-x-0" : "-translate-x-full"
				} flex flex-col shadow-2xl`}
				style={{
					opacity: openMenuMobile ? "1" : "0",
					visibility: openMenuMobile ? "visible" : "hidden",
					pointerEvents: openMenuMobile ? "auto" : "none",
				}}
			>
				{/* 头部 */}
				<div className="flex items-center justify-between bg-white px-6 py-5">
					<Link href="/" onClick={handleMenuMobile}>
						<Image
							src="/image/logo.webp"
							width={120}
							height={32}
							alt="PinShop Saleor Logo"
							className="h-8 w-auto"
						/>
					</Link>
					<button
						onClick={handleMenuMobile}
						className="p-1 text-gray-400 transition-colors hover:text-gray-600"
						aria-label="关闭菜单"
					>
						<i className="ri-close-line text-2xl"></i>
					</button>
				</div>

				{/* 菜单列表 */}
				<div className="flex-1 overflow-y-auto bg-white">
					<nav className="px-0">
						{menus.map(
							(item) =>
								item.show && (
									<div key={item.id} className="border-b border-gray-100 last:border-b-0">
										<button
											onClick={() => {
												if (item.children && item.children.length > 0) {
													toggleExpanded(String(item.id));
												} else {
													GoToPage(item.link);
												}
											}}
											className="flex w-full items-center justify-between px-6 py-4 text-left text-gray-900 transition-colors hover:bg-gray-50"
										>
											<span className="text-[16px] font-medium">{t(item.name)}</span>
											{item.children && item.children.length > 0 ? (
												<i
													className={`text-xl transition-all duration-200 ${
														expandedItems.includes(String(item.id))
															? "ri-subtract-line text-red-500"
															: "ri-add-line text-gray-400"
													}`}
												></i>
											) : (
												<i className="ri-arrow-right-s-line text-xl text-gray-400"></i>
											)}
										</button>

										{/* 子菜单 */}
										{item.children && item.children.length > 0 && (
											<div
												className={`overflow-hidden bg-gray-50 transition-all duration-300 ease-in-out ${
													expandedItems.includes(String(item.id))
														? "max-h-96 opacity-100"
														: "max-h-0 opacity-0"
												}`}
											>
												{item.children.map((child: any, index: number) => (
													<button
														key={child.id}
														onClick={() => GoToPage(child.link)}
														className="flex w-full items-center border-b border-gray-200 px-8 py-3 text-left text-gray-700 transition-all duration-150 last:border-b-0 hover:bg-white hover:text-red-500"
														style={{
															animationDelay: `${index * 50}ms`,
														}}
													>
														<span className="text-[14px]">{t(child.name)}</span>
													</button>
												))}
											</div>
										)}
									</div>
								),
						)}
					</nav>
				</div>

				{/* 底部区域 */}
				<div className="mt-auto flex justify-between gap-4 bg-white px-6 py-8">
					{/* 登录 */}
					<button
						onClick={onLoginClick}
						className=" block w-full rounded-lg border border-black px-6 py-2 text-center font-medium text-black transition-colors hover:bg-gray-50"
					>
						登录
					</button>
					{/* 免费试用按钮 */}
					<button
						onClick={onRegisterClick}
						className=" block w-full rounded-lg border border-black px-6 py-2 text-center font-medium text-black transition-colors hover:bg-gray-50"
					>
						免费试用
					</button>
				</div>
			</div>
		</>
	);
}
