"use client";

import React, { useEffect, useMemo, useState, useRef, lazy, Suspense, startTransition } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import Image from "next/image";
import { CaretLeft, CaretRight } from "@phosphor-icons/react";
import { Button, Empty, Skeleton } from "antd";
import { ProductZoom } from "./product-zoom";
import { useLocale, useTranslations } from "next-intl";
import axios from "axios";
import { defaultLocale } from "@/config";
import { productTranslationName } from "@/lib/utils/util";

const imageWh = 610;
const imageH = 688;
const spaceBetween = 8;
const slidesPerView = 5;

interface ProductMedia {
	id: string;
	name: string;
	alt: string;
	type: string;
	url: string;
}

//默认数据
const defaultImage: ProductMedia[] = [
	{
		id: "default",
		name: "default-product.webp",
		alt: "default product image",
		type: "image/webp",
		url: "/image/default-image.webp",
	},
];

const getImagesForProduct = (product: any): ProductMedia[] => {
	if (!product?.metadata) return defaultImage;
	try {
		const media = JSON.parse(product.media);
		// @ts-ignore
		return Array.isArray(media) && media.length > 0 ? media : defaultImage;
	} catch (error) {
		console.error("Error parsing product media:", error);
		return defaultImage;
	}
};

interface ImageGalleryProps {
	product: any;
}

// 添加懒加载导入
const LoadNormalModal = lazy(() => import("../3DNormalModal"));

function ImageGallery({ product }: ImageGalleryProps) {
	const imagesList = getImagesForProduct(product);
	const videoRef = useRef<HTMLVideoElement>(null);
	const [preloaded3D, setPreloaded3D] = useState(false);

	const [active, setActive] = useState(0);
	const [mainImage, setMainImage] = useState<string>(defaultImage[0].url);
	const [videoUrl, setvideoUrl] = useState<string>("");
	const [isPlaying, setIsPlaying] = useState(false);
	const [images, setImages] = useState<ProductMedia[]>(defaultImage);
	const [swiperExample, setSwiperExample] = useState<any>();
	const [ActiveShowType, setActiveShowType] = useState<"img" | "mp4" | "3d">("img");
	const t = useTranslations();
  let locale=useLocale()
	const [tdEdit, _] = useState(() => {
		try {
			const parsedData = JSON.parse(product.threeModeCustoms || "[]") as any[];
			return parsedData
				.filter((item) => item.enable)
				.map((item) => ({
					...item,
				}));
		} catch (error) {
			console.error("解析 threeModeCustoms 失败:", error);
			return [];
		}
	}) as any;
	// 初始化图片列表
	const changeProductImages = (product: any) => {
		const imagesFromProduct = getImagesForProduct(product);
		setImages(imagesFromProduct);
		setMainImage(imagesFromProduct[0]?.url || defaultImage[0].url);
	};

  useEffect(() => {
    if (product) {
      setActive(0);
      getvideo();
      // 修改预加载逻辑
      if (product.metadata) {
        const threeModelJson = product.metadata.find((item) => item.key === "threeModel");
        if (threeModelJson) {
          const threeModel = JSON.parse(threeModelJson.value)[0];
          if (threeModel && ActiveShowType === "3d") {
            // 先进行数据获取
            fetch(process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL + `/medias/file_download/` + threeModel.id)
              .then((response) => response.arrayBuffer())
              .then(() => {
                // 使用 startTransition 包装状态更新
                startTransition(() => {
                  setPreloaded3D(true);
                });
              })
              .catch((error) => {
                console.error("Error preloading 3D model:", error);
              });
          }
        }
      }
      changeProductImages(product);
    }
  }, [product, ActiveShowType]);
	//商品添加视屏
	async function getvideo() {
		try {
			const coverMp4Obj = JSON.parse(
				product.metadata.filter((item) => item.key === "cover_mp4")[0]?.value || "[]",
			)[0];
			if (coverMp4Obj) {
				const { data } = await axios(
					`${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/medias/video/${coverMp4Obj.id}`,
				);
				setvideoUrl(data.detail.video_url);
			}
		} catch (error) {
			console.error("Error fetching video URL:", error);
		}
	}

	const handlePrevOrNext = (type: "prev" | "next") => {
		if (type === "prev") {
			setActive((value) => {
				if (value === 0) return value;
				setMainImage(images[value - 1].url || defaultImage[0].url);
				swiperExample?.slidePrev();
				return value - 1;
			});
		} else {
			setActive((value) => {
				if (value + 1 > images.length - 1) return value;
				setMainImage(images[value + 1].url || defaultImage[0].url);
				if (value + 1 > slidesPerView - 1) {
					swiperExample?.slideNext();
				}
				return value + 1;
			});
		}
	};

	const setActiveImgIndex = (index: number) => {
		setActive((val) => {
			if (images.length > slidesPerView) {
				const prev = document.querySelector(".button-prev") as HTMLElement;
				const next = document.querySelector(".button-next") as HTMLElement;
				index > val ? next?.click() : prev?.click();
			}
			return index;
		});
	};

	const threeModel = useMemo<any>(() => {
		const json = product.metadata?.find((item) => item.key === "threeModel");
		return json ? JSON.parse(json.value)[0] : null;
	}, [product]);

	// const [threeDisplay, setThreeDisplay] = useState(true);
	const handleDisplay = () => {
		// setThreeDisplay(!threeDisplay);
		if (tdEdit.length > 0) {
			window.scrollTo({
				top: 800,
				behavior: "smooth",
			});
		} else {
			setActiveShowType("3d");
		}
	};

	return (
		<div className="flex-colr flex w-full select-none justify-between">
			<div className="relative box-border flex flex-1 gap-10 rounded-xl max-lg:gap-5 max-md:gap-x-3">
				<div className="relative flex w-[88px] flex-col">
					{images.length > 0 && (
						<>
							<Swiper
								onSwiper={setSwiperExample}
								className="flex h-[600px] w-full !flex-col max-lg:h-[500px] max-md:h-[300px]"
								spaceBetween={spaceBetween}
								slidesPerView={slidesPerView}
								navigation={{
									nextEl: ".button-next",
									prevEl: ".button-prev",
								}}
								direction="vertical"
							>
								{images?.map((image: any, index: number) => (
									<SwiperSlide key={index} className="relative">
										{videoUrl && index === 0 && (
											<div
												onClick={(e) => {
													e.stopPropagation();
													setIsPlaying(!isPlaying);
													setActiveShowType("mp4");
													if (videoRef.current) {
														if (isPlaying) {
															videoRef.current.pause();
														} else {
															videoRef.current.play();
														}
													}
												}}
												className="absolute left-1/2 top-1/2 z-[99999] flex h-10 w-10 -translate-x-1/2 -translate-y-1/2 transform cursor-pointer items-center justify-center rounded-full bg-black/35 max-md:left-8"
											>
												<i
													className={`${isPlaying ? "ri-pause-fill" : "ri-play-fill"} text-2xl text-white`}
												></i>
											</div>
										)}

										<div
											className={`!flex h-fit w-fit cursor-pointer !flex-col overflow-hidden rounded-md border-[3px] duration-300 ${
												active === index ? "border-black" : "border-themeSecondary200"
											}`}
											onClick={() => {
												setMainImage(image.url || defaultImage[0].url);
												setActive(index);
												setActiveShowType("img");
											}}
										>
											<div className="relative hidden aspect-square object-cover sm:flex sm:items-center sm:justify-center">
												<Image
													quality={100}
													src={image.url || defaultImage[0].url}
													width={275}
													height={300}
													alt={locale === defaultLocale ? product?.name :productTranslationName(product?.translation?.name) || product?.name}
												/>
											</div>
											<div className="relative flex aspect-square sm:hidden">
												<Image
													quality={100}
													src={image.url || defaultImage[0].url}
													width={62}
													height={71}
                          alt={locale === defaultLocale ? product?.name :productTranslationName(product?.translation?.name) || product?.name}
												/>
											</div>
										</div>
									</SwiperSlide>
								))}
							</Swiper>
							{images.length > 6 && (
								<div className="modern-scroll-indicator pt-6 max-md:hidden">
									<button
										className="scroll-trigger"
										onClick={() => {
											if (swiperExample && active < images.length - 1) {
												swiperExample.slideNext();
												setActive((prev) => Math.min(prev + 1, images.length - 1));
												setMainImage(images[active + 1]?.url || defaultImage[0].url);
											}
										}}
									>
										<div className="scroll-content">
											<div className="scroll-dots">
												<span className="dot"></span>
												<span className="dot"></span>
												<span className="dot"></span>
											</div>
											<div className="scroll-line"></div>
											<div className="scroll-arrow">
												<i className="ri-arrow-down-line"></i>
											</div>
										</div>
										<div className="hover-text text-[12px]">{t("nav.Swipe for more")}</div>
									</button>
								</div>
							)}
						</>
					)}
				</div>

				<div
					className={`relative z-10 h-[688px] w-[610px] transition duration-300 ease-in-out max-md:h-[300px] max-md:w-[255px] `}
				>
					{/* 图片显示 */}
					<div
						className={`absolute right-0 top-0 h-full w-full transition-opacity duration-300 ${
							ActiveShowType === "img" ? "pointer-events-auto opacity-100" : "pointer-events-none opacity-0"
						}`}
					>
						<div className="group flex h-[688px] w-[608px] items-center justify-center border-[1px] border-[#ddd] max-md:pointer-events-none max-md:!h-[290px] max-md:!w-[255px]">
							{/* <ProductZoom
								src={mainImage || defaultImage[0].url}
								imageWidth={imageWh}
								imageHeight={imageH}
								setActiveImgIndex={setActiveImgIndex}
								quality={100}
								imgList={images.map(img => img.url)}
								fit="contain"
							/> */}
							<Image
								src={mainImage || defaultImage[0].url}
								alt={"contain"}
								quality={100}
								width={imageWh}
								height={imageH}
								layout="intrinsic"
								objectFit="contain"
								priority={false}
							/>
						</div>
					</div>

					{/* 视频显示 */}
					<div
						className={`absolute right-0 top-0 h-full w-full transition-opacity duration-300 ${
							ActiveShowType === "mp4" ? "pointer-events-auto opacity-100" : "pointer-events-none opacity-0"
						}`}
					>
						{videoUrl && (
							<video
								ref={videoRef}
								src={videoUrl}
								poster={mainImage || defaultImage[0].url}
								autoPlay={isPlaying}
								loop
								muted
								playsInline
								className="h-[688px] w-[608px] object-cover max-md:!h-[290px] max-md:!w-[268px]"
							></video>
						)}
					</div>

					{/* 3D模型显示 */}
					<div
						className={`absolute right-0 top-0 h-full w-full transition-opacity duration-300 ${
							ActiveShowType === "3d" ? "pointer-events-auto opacity-100" : "pointer-events-none opacity-0"
						}`}
					>
						{threeModel && (
							<div
								className={`h-[688px] w-[608px] rounded-none bg-[#f5f5f5] max-md:!h-[290px] max-md:!w-[255px]`}
							>
								<Suspense
									fallback={
										<div className="flex h-full w-full items-center justify-center">Loading 3D Model...</div>
									}
								>
									<LoadNormalModal
										backgroundColor={threeModel.backgroundColor}
										modalUrl={
											process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL + `/medias/file_download/` + threeModel.id
										}
										className="h-full w-full"
										fileName={threeModel.name}
										metalness={threeModel.metalness}
										color={threeModel.color}
										roughness={threeModel.roughness}
									/>
								</Suspense>
							</div>
						)}
					</div>

					{process.env.NEXT_PUBLIC_IS_3D === "true" && threeModel && (
						<div className="absolute right-0 top-0 z-20 box-border p-5">
							<div className="h-full w-full">
								<button
									className="tooltip-icon group !flex h-[50px] w-[50px] !items-center !justify-center rounded-full bg-white hover:bg-black"
									onClick={handleDisplay}
								>
									<RotatingCube />
								</button>
							</div>
						</div>
					)}
				</div>
			</div>
		</div>
	);
}

export default React.memo(ImageGallery);

function RotatingCube() {
	return (
		<div className="cube-container-small">
			<div className="cube-small">
				<div className="cube-face cube-face-front">F</div>
				<div className="cube-face cube-face-back">B</div>
				<div className="cube-face cube-face-right">R</div>
				<div className="cube-face cube-face-left">L</div>
				<div className="cube-face cube-face-top">T</div>
				<div className="cube-face cube-face-bottom">Bo</div>
			</div>
		</div>
	);
}

<style jsx>{`
	.modern-scroll-indicator {
		padding-top: 1.5rem;
		display: flex;
		justify-content: center;
	}

	.scroll-trigger {
		position: relative;
		background: transparent;
		border: none;
		cursor: pointer;
		padding: 8px;
		transition: transform 0.3s ease;
	}

	.scroll-content {
		position: relative;
		width: 44px;
		height: 44px;
		background: rgba(0, 0, 0, 0.85);
		backdrop-filter: blur(8px);
		border-radius: 50%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.scroll-dots {
		display: flex;
		flex-direction: column;
		gap: 3px;
		margin-bottom: 4px;
	}

	.dot {
		width: 3px;
		height: 3px;
		background: white;
		border-radius: 50%;
		opacity: 0.7;
	}

	.dot:nth-child(1) {
		animation: fadeInOut 1.5s infinite 0s;
	}
	.dot:nth-child(2) {
		animation: fadeInOut 1.5s infinite 0.2s;
	}
	.dot:nth-child(3) {
		animation: fadeInOut 1.5s infinite 0.4s;
	}

	.scroll-line {
		width: 1px;
		height: 0;
		background: rgba(255, 255, 255, 0.3);
		transition: height 0.3s ease;
	}

	.scroll-arrow {
		position: absolute;
		bottom: 8px;
		color: white;
		opacity: 0;
		transform: translateY(4px);
		transition: all 0.3s ease;
	}

	.hover-text {
		position: absolute;
		top: 100%;
		left: 50%;
		transform: translateX(-50%);
		font-size: 12px;
		color: #666;
		margin-top: 8px;
		white-space: nowrap;
		opacity: 0;
		transition: opacity 0.3s ease;
	}

	.scroll-trigger:hover .scroll-content {
		background: rgba(0, 0, 0, 0.95);
		transform: translateY(-2px);
		box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
	}

	.scroll-trigger:hover .scroll-line {
		height: 12px;
	}

	.scroll-trigger:hover .scroll-arrow {
		opacity: 1;
		transform: translateY(0);
	}

	.scroll-trigger:hover .hover-text {
		opacity: 1;
	}

	@keyframes fadeInOut {
		0%,
		100% {
			opacity: 0.3;
			transform: translateY(0);
		}
		50% {
			opacity: 1;
			transform: translateY(2px);
		}
	}
`}</style>;
