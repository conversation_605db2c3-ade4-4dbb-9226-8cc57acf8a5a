import React from "react";
import { type MyPageProps } from "@/lib/@types/base";
import type { Metadata } from "next";
import { getBasePageSeo } from "@/lib/api/seo";
import { generateSeo } from "@/lib/utils/seo";
import { unstable_setRequestLocale } from "next-intl/server";
import Banner from "@/components/ProductPage/Banner";
import Introduction from "@/components/ProductPage/Introduction";
import Accordion from "@/components/ProductPage/Accordion";

export const generateMetadata = async (props: MyPageProps): Promise<Metadata> => {
	props.params.page = ["products"];
	const seo = await getBasePageSeo(props);
	return generateSeo(props, {
		...seo,
		ogType: "website",
	});
};

export default async function Products({ params }: MyPageProps) {
	const locale = params.locale;
	unstable_setRequestLocale(locale);
	return (
		<main>
			<Banner />
			<Introduction />
			<Accordion />
		</main>
	);
}
