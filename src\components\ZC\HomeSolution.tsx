"use client";
import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { useHeaderHeight } from "@/lib/hooks/useHeaderHeight";
import LoginModal from "@/components/User/login-modal";
import { useTranslations } from "next-intl";
import { Link } from "@/navigation";
// 可复用的按钮组件
interface ButtonGroupProps {
	buttons: string[];
	className?: string;
}

const ButtonGroup: React.FC<ButtonGroupProps> = ({ buttons, className = "" }) => {
	return (
		<div className={`mt-8 flex flex-wrap gap-3 ${className}`}>
			{buttons.map((buttonText, index) => (
				<Link
					href="/"
					key={index}
					className="rounded  border border-black px-4 py-2 text-sm text-titleLight transition-colors  hover:text-[#555] "
				>
					{buttonText}
				</Link>
			))}
		</div>
	);
};

export default function HomeSolution() {
	return (
		<>
			<section className="bg-[#c7e3ff99]  py-16 md:py-24">
				<div className="container">
					{/* 标题 */}
					<motion.div 
						initial={{ opacity: 0, y: 30 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.6 }}
						viewport={{ once: true }}
						className="mb-20 text-center"
					>
						<h2 className="mb-4 text-2xl font-bold text-gray-800 md:text-3xl lg:text-4xl">
							一站式跨境电商解决方案
						</h2>
						{/* <p className="text-titleLight text-lg max-md:text-base">我们采用 Next.js 与 React 技术构建网站，默认优化 SEO、性能与可扩展性，支持一键部署至 Vercel，快速上线。</p> */}
					</motion.div>

					{/* 第一部分：轻松开启全球网店 */}
					<div className="mb-24">
						<div className="grid items-center gap-12 lg:grid-cols-2">
							{/* 左侧：浏览器界面 */}
							<motion.div 
								initial={{ opacity: 0, x: -50 }}
								whileInView={{ opacity: 1, x: 0 }}
								transition={{ duration: 0.8, delay: 0.2 }}
								viewport={{ once: true }}
								className="order-2 lg:order-1"
							>
								<SEOOptimizedImage
									src="https://www.shoplazza.cn/wstmart/home/<USER>/default/images/solution_1.png"
									width={1000}
									height={1000}
									alt="Pinshop solution Home Banner 1"
									className="w-full object-contain"
								></SEOOptimizedImage>
							</motion.div>

							{/* 右侧：功能特点 */}
							<motion.div 
								initial={{ opacity: 0, x: 50 }}
								whileInView={{ opacity: 1, x: 0 }}
								transition={{ duration: 0.8, delay: 0.4 }}
								viewport={{ once: true }}
								className="order-1 lg:order-2 lg:px-[80px]"
							>
								<motion.h3 
									initial={{ opacity: 0, y: 20 }}
									whileInView={{ opacity: 1, y: 0 }}
									transition={{ duration: 0.6, delay: 0.6 }}
									viewport={{ once: true }}
									className="mb-8 text-2xl font-bold text-gray-800 md:text-3xl"
								>
									技术优势
								</motion.h3>
								<motion.h4 
									initial={{ opacity: 0, y: 20 }}
									whileInView={{ opacity: 1, y: 0 }}
									transition={{ duration: 0.6, delay: 0.8 }}
									viewport={{ once: true }}
									className="text-titleLight/50 text-lg max-md:text-base mb-4"
								>
									我们采用 Next.js 与 React 技术构建网站，默认优化 SEO、性能与可扩展性，支持一键部署至 Vercel，快速上线。
								</motion.h4>
								<div className="space-y-6">
									{/* 功能点1 */}
									<motion.div 
										initial={{ opacity: 0, y: 30 }}
										whileInView={{ opacity: 1, y: 0 }}
										transition={{ duration: 0.6, delay: 1.0 }}
										viewport={{ once: true }}
										className="flex items-start space-x-4"
									>
										<div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-red-500">
											<span className="text-sm font-bold text-white">1</span>
										</div>
										<div>
											<h4 className="mb-2 text-[20px] text-titleLight">极速体验，由 Pinshop 技术驱动</h4>
											<p className="text-[14px] leading-relaxed text-titleLight">
												· 支持服务端渲染与静态生成
												<br />
												· 自动代码拆分与懒加载
												<br />
												· 一键部署至 Vercel
												<br />
												· 内置 SEO 与性能优化机制
												<br />· 可接入无头 CMS 实现灵活管理
											</p>
										</div>
									</motion.div>

									{/* 功能点2 */}
									<motion.div 
										initial={{ opacity: 0, y: 30 }}
										whileInView={{ opacity: 1, y: 0 }}
										transition={{ duration: 0.6, delay: 1.2 }}
										viewport={{ once: true }}
										className="flex items-start space-x-4"
									>
										<div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-red-500">
											<span className="text-sm font-bold text-white">2</span>
										</div>
										<div>
											<h4 className="mb-2 text-[20px] text-titleLight">轻松实现本土化多语言</h4>
											<p className="text-[14px] leading-relaxed text-titleLight">
												· 一键翻译多种，系统自动识别用户地区语言
												<br />
												· AI+人工双重本土化翻译服务
												<br />· 满足200+国家多种语言需求
											</p>
										</div>
									</motion.div>

									{/* 功能点3 */}
									<motion.div 
										initial={{ opacity: 0, y: 30 }}
										whileInView={{ opacity: 1, y: 0 }}
										transition={{ duration: 0.6, delay: 1.4 }}
										viewport={{ once: true }}
										className="flex items-start space-x-4"
									>
										<div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-red-500">
											<span className="text-sm font-bold text-white">3</span>
										</div>
										<div>
											<h4 className="mb-2 text-[20px] text-titleLight">安全稳定的支付和配送</h4>
											<p className="text-[14px] leading-relaxed text-titleLight">
												· 对接PayPal主流支付平台
												<br />
												· 支持Visa等国际一流卡,满足全球客户
											</p>
										</div>
									</motion.div>
								</div>

								{/* 按钮组 */}
								{/* <ButtonGroup
									buttons={[
										"网站建设 >",
										"网站优化 >",
										"营销推广 >",
										"IT系统开发 >",
										"数据分析 >",
										"系统定制 >",
									]}
								/> */}
							</motion.div>
						</div>
					</div>

					{/* 第二部分：多渠道引流获客 */}
					<div>
						<div className="grid items-center gap-12 lg:grid-cols-2">
							{/* 左侧：功能列表 */}
							<motion.div 
								initial={{ opacity: 0, x: -50 }}
								whileInView={{ opacity: 1, x: 0 }}
								transition={{ duration: 0.8, delay: 0.2 }}
								viewport={{ once: true }}
								className="lg:px-[80px]"
							>
								<motion.h3 
									initial={{ opacity: 0, y: 20 }}
									whileInView={{ opacity: 1, y: 0 }}
									transition={{ duration: 0.6, delay: 0.4 }}
									viewport={{ once: true }}
									className="mb-8 text-2xl font-bold text-gray-800 md:text-3xl"
								>
									适用场景
								</motion.h3>
								<motion.h4 
									initial={{ opacity: 0, y: 20 }}
									whileInView={{ opacity: 1, y: 0 }}
									transition={{ duration: 0.6, delay: 0.6 }}
									viewport={{ once: true }}
									className="text-titleLight/50 text-lg max-md:text-base mb-4"
								>
									Pinshop 面向初创企业、SaaS 团队、独立品牌与出海商家，提供灵活高效、面向增长的网站建站服务。
								</motion.h4>
								<div className="space-y-6">
									{/* 功能点1 */}
									<motion.div 
										initial={{ opacity: 0, y: 30 }}
										whileInView={{ opacity: 1, y: 0 }}
										transition={{ duration: 0.6, delay: 0.8 }}
										viewport={{ once: true }}
										className="flex items-start space-x-4"
									>
										<div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-red-500">
											<span className="text-sm font-bold text-white">1</span>
										</div>
										<div>
											<h4 className="mb-2 text-[20px] text-titleLight">覆盖官网、电商、展示站等多种建站需求</h4>
											<p className="text-[14px] leading-relaxed text-titleLight">
												· 企业官网
												<br />
												· SaaS 产品官网
												<br />
												· 产品展示网站
												<br />
												· 独立电商网站
												<br />
												· 多语种出海站点
											</p>
										</div>
									</motion.div>

									{/* 功能点2 */}
									{/* <div className="flex items-start space-x-4">
										<div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-red-500">
											<span className="text-sm font-bold text-white">2</span>
										</div>
										<div>
											<h4 className="mb-2 text-[20px] text-titleLight">精准投放提升广告表现</h4>
											<p className="text-[14px] leading-relaxed text-titleLight">
												· Pixels一键安装，不限制投放账户数量
												<br />
												· 广告投放全流程，自动优化广告
												<br />· GA追踪数据分析，优化投放策略
											</p>
										</div>
									</div> */}

									{/* 功能点3 */}
									<motion.div 
										initial={{ opacity: 0, y: 30 }}
										whileInView={{ opacity: 1, y: 0 }}
										transition={{ duration: 0.6, delay: 1.0 }}
										viewport={{ once: true }}
										className="flex items-start space-x-4"
									>
										<div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-red-500">
											<span className="text-sm font-bold text-white">2</span>
										</div>
										<div>
											<h4 className="mb-2 text-[20px] text-titleLight">自动化SEO工具提升搜索排名</h4>
											<p className="text-[14px] leading-relaxed text-titleLight">
												· Meta标签自动生成，自动优化关键词
												<br />
												· 智能SEO优化，内容智能生成
												<br />
												{/* · 一键下载Sitemap，快速提升搜索排名 */}
											</p>
										</div>
									</motion.div>

									{/* 功能点4 */}
									<motion.div 
										initial={{ opacity: 0, y: 30 }}
										whileInView={{ opacity: 1, y: 0 }}
										transition={{ duration: 0.6, delay: 1.2 }}
										viewport={{ once: true }}
										className="flex items-start space-x-4"
									>
										<div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-red-500">
											<span className="text-sm font-bold text-white">3</span>
										</div>
										<div>
											<h4 className="mb-2 text-[20px] text-titleLight">数据营销</h4>
											<p className="text-[14px] leading-relaxed text-titleLight">
												{/* · 海量客户数据，快速建立客户画像 */}
												{/* <br /> */}
												{/* · 实时监控客户行为，精准分析客户需求 */}
												{/* <br /> */}
												· 营销自动化工具，高效提升广告回报
											</p>
										</div>
									</motion.div>
								</div>

								{/* 按钮组 */}
								{/* <ButtonGroup
									buttons={["外贸建站服务", "Google推广", "海外营销", "Facebook推广", "海外推广服务"]}
								/> */}
							</motion.div>

							{/* 右侧：手机界面展示 */}
							<motion.div 
								initial={{ opacity: 0, x: 50 }}
								whileInView={{ opacity: 1, x: 0 }}
								transition={{ duration: 0.8, delay: 0.4 }}
								viewport={{ once: true }}
								className=""
							>
								<SEOOptimizedImage
									src="https://www.shoplazza.cn/wstmart/home/<USER>/default/images/solution_2.png"
									alt="手机界面展示"
									width={1000}
									height={100}
									className="h-full w-full object-contain"
								></SEOOptimizedImage>
							</motion.div>
						</div>
					</div>
				</div>
			</section>
			<section className="bg-mainPrimaryLight  py-16 md:py-24">
				<div className="container">
					{/* 第三部分：轻松开启全球网店 */}
					<div className="mb-24">
						<div className="grid items-center gap-12 lg:grid-cols-2">
							{/* 左侧：浏览器界面 */}
							<motion.div 
								initial={{ opacity: 0, x: -50 }}
								whileInView={{ opacity: 1, x: 0 }}
								transition={{ duration: 0.8, delay: 0.2 }}
								viewport={{ once: true }}
								className="order-2 lg:order-1"
							>
								<SEOOptimizedImage
									src="https://www.shoplazza.cn/wstmart/home/<USER>/default/images/solution_3.png"
									width={1000}
									height={1000}
									alt="Pinshop solution Home Banner 1"
									className="w-full object-contain"
								></SEOOptimizedImage>
							</motion.div>

							{/* 右侧：功能特点 */}
							<motion.div 
								initial={{ opacity: 0, x: 50 }}
								whileInView={{ opacity: 1, x: 0 }}
								transition={{ duration: 0.8, delay: 0.4 }}
								viewport={{ once: true }}
								className="order-1 lg:order-2 lg:px-[80px]"
							>
								<motion.h3 
									initial={{ opacity: 0, y: 20 }}
									whileInView={{ opacity: 1, y: 0 }}
									transition={{ duration: 0.6, delay: 0.6 }}
									viewport={{ once: true }}
									className="mb-8 text-2xl font-bold text-gray-800 md:text-3xl"
								>
									AI 智能能力
								</motion.h3>
								<motion.h4 
									initial={{ opacity: 0, y: 20 }}
									whileInView={{ opacity: 1, y: 0 }}
									transition={{ duration: 0.6, delay: 0.8 }}
									viewport={{ once: true }}
									className="text-titleLight/50 text-lg max-md:text-base mb-4"
								>
									每一个 Pinshop 网站都可选配 AI 内容能力，从自动 SEO 到多语种视频，提升效率与传播力，助你轻松增长。
								</motion.h4>
								<div className="space-y-6">
									{/* 功能点1 */}
									<motion.div 
										initial={{ opacity: 0, y: 30 }}
										whileInView={{ opacity: 1, y: 0 }}
										transition={{ duration: 0.6, delay: 1.0 }}
										viewport={{ once: true }}
										className="flex items-start space-x-4"
									>
										<div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-red-500">
											<span className="text-sm font-bold text-white">1</span>
										</div>
										<div>
											<h4 className="mb-2 text-[20px] text-titleLight">不止建站，更让内容智能增长</h4>
											<p className="text-[14px] leading-relaxed text-titleLight">
												· 自动生成标题、描述与结构，提升收录效率
												<br />
												· 用户行为识别，智能弹出询盘框
												<br />
												· 7x24 多语言网站接待数字人
												<br />
												· 产品图转短视频，提升展示力
												<br />
												· 多语字幕与配音自动生成
												<br />
												· 视频重构，适配高清多平台发布
												<br />
												· 视频重构，适配高清多平台发布
											</p>
										</div>
									</motion.div>

									{/* 功能点2 */}
									<motion.div 
										initial={{ opacity: 0, y: 30 }}
										whileInView={{ opacity: 1, y: 0 }}
										transition={{ duration: 0.6, delay: 1.2 }}
										viewport={{ once: true }}
										className="flex items-start space-x-4"
									>
										<div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-red-500">
											<span className="text-sm font-bold text-white">2</span>
										</div>
										<div>
											<h4 className="mb-2 text-[20px] text-titleLight">智能用户交互体验</h4>
											<p className="text-[14px] leading-relaxed text-titleLight">
												· 用户行为识别，智能弹出询盘框与引导
												<br />
												· 7x24小时多语言网站接待数字人
												<br />
												· 智能客服机器人，自动回答常见问题
												<br />
												· 个性化推荐引擎，提升用户转化率
												<br />
												· 智能A/B测试，自动优化页面效果
											</p>
										</div>
									</motion.div>

									{/* 功能点3 */}
									{/* <div className="flex items-start space-x-4">
										<div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-red-500">
											<span className="text-sm font-bold text-white">3</span>
										</div>
										<div>
											<h4 className="mb-2 text-[20px] text-titleLight">多媒体智能处理</h4>
											<p className="text-[14px] leading-relaxed text-titleLight">
												· 产品图片自动转短视频，提升展示力
												<br />
												· 多语言字幕与配音自动生成
												<br />
												· 视频智能重构，适配高清多平台发布
												<br />
												· 支持3D产品展示，提升用户体验
												<br />
												· 智能图片优化，自动压缩与格式转换
											</p>
										</div>
									</div> */}

									{/* 功能点4 */}
									<motion.div 
										initial={{ opacity: 0, y: 30 }}
										whileInView={{ opacity: 1, y: 0 }}
										transition={{ duration: 0.6, delay: 1.4 }}
										viewport={{ once: true }}
										className="flex items-start space-x-4"
									>
										<div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-red-500">
											<span className="text-sm font-bold text-white">3</span>
										</div>
										<div>
											<h4 className="mb-2 text-[20px] text-titleLight">数据分析与优化</h4>
											<p className="text-[14px] leading-relaxed text-titleLight">
												· 智能数据分析，实时监控网站表现
												<br />
												· 用户行为深度分析，精准优化转化路径
												<br />
												· 自动生成数据报告，提供决策支持
												<br />
												· 智能营销策略推荐，提升ROI
												<br />
												· 预测性分析，提前识别市场趋势
											</p>
										</div>
									</motion.div>
								</div>

								{/* 按钮组 */}
								{/* <ButtonGroup
									buttons={[
										"购物车 >",
										"结账页 >",
										"支付渠道 >",
										"免费试用 >",
									]}
								/> */}
							</motion.div>
						</div>
					</div>

					{/* 第四部分：多渠道引流获客 */}
					<div>
						<div className="grid items-center gap-12 lg:grid-cols-2">
							{/* 左侧：功能列表 */}
							<motion.div 
								initial={{ opacity: 0, x: -50 }}
								whileInView={{ opacity: 1, x: 0 }}
								transition={{ duration: 0.8, delay: 0.2 }}
								viewport={{ once: true }}
								className="lg:px-[80px]"
							>
								<motion.h3 
									initial={{ opacity: 0, y: 20 }}
									whileInView={{ opacity: 1, y: 0 }}
									transition={{ duration: 0.6, delay: 0.4 }}
									viewport={{ once: true }}
									className="mb-8 text-2xl font-bold text-gray-800 md:text-3xl"
								>
									模板展示
								</motion.h3>
								<motion.h4 
									initial={{ opacity: 0, y: 20 }}
									whileInView={{ opacity: 1, y: 0 }}
									transition={{ duration: 0.6, delay: 0.6 }}
									viewport={{ once: true }}
									className="text-titleLight/50 text-lg max-md:text-base mb-4"
								>
									挑选经过行业设计优化的模板，移动端适配、SEO 结构完善，快速发布国际化站点。
								</motion.h4>
								<div className="space-y-6">
									{/* 功能点1 */}
									<motion.div 
										initial={{ opacity: 0, y: 30 }}
										whileInView={{ opacity: 1, y: 0 }}
										transition={{ duration: 0.6, delay: 0.8 }}
										viewport={{ once: true }}
										className="flex items-start space-x-4"
									>
										<div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-red-500">
											<span className="text-sm font-bold text-white">1</span>
										</div>
										<div>
											<h4 className="mb-2 text-[20px] text-titleLight">丰富模板，快速起站</h4>
											<p className="text-[14px] leading-relaxed text-titleLight">
												· 行业专属模板，覆盖电商、企业、SaaS等场景
												<br />
												· 移动端自适应，完美适配各种设备
												<br />
												· 模块可视化编辑，拖拽式建站体验
												<br />
												· 内置 SEO 结构，优化搜索引擎收录
												<br />
												· 上线前实时预览，所见即所得
											</p>
										</div>
									</motion.div>

									{/* 功能点2 */}
									<motion.div 
										initial={{ opacity: 0, y: 30 }}
										whileInView={{ opacity: 1, y: 0 }}
										transition={{ duration: 0.6, delay: 1.0 }}
										viewport={{ once: true }}
										className="flex items-start space-x-4"
									>
										<div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-red-500">
											<span className="text-sm font-bold text-white">2</span>
										</div>
										<div>
											<h4 className="mb-2 text-[20px] text-titleLight">个性化定制，品牌专属</h4>
											<p className="text-[14px] leading-relaxed text-titleLight">
												· 品牌色彩与字体自定义，打造专属视觉
												<br />
												· 多语言模板支持，一键切换不同语言版本
												<br />
												· 响应式设计，完美适配PC、平板、手机
											</p>
										</div>
									</motion.div>

									{/* 功能点3 */}
									{/* <div className="flex items-start space-x-4">
										<div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-red-500">
											<span className="text-sm font-bold text-white">3</span>
										</div>
										<div>
											<h4 className="mb-2 text-[20px] text-titleLight">专业组件库，功能丰富</h4>
											<p className="text-[14px] leading-relaxed text-titleLight">
												· 产品展示、轮播图、表单等丰富组件
												<br />
												· 社交媒体集成，一键分享到各大平台
												<br />
												· 数据分析组件，实时监控网站表现
											</p>
										</div>
									</div> */}

									{/* 功能点4 */}
									<motion.div 
										initial={{ opacity: 0, y: 30 }}
										whileInView={{ opacity: 1, y: 0 }}
										transition={{ duration: 0.6, delay: 1.2 }}
										viewport={{ once: true }}
										className="flex items-start space-x-4"
									>
										<div className="mt-1 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-red-500">
											<span className="text-sm font-bold text-white">3</span>
										</div>
										<div>
											<h4 className="mb-2 text-[20px] text-titleLight">快速部署，一键上线</h4>
											<p className="text-[14px] leading-relaxed text-titleLight">
												· 一键部署至Vercel平台
												<br />
												· 自动生成Sitemap，提升搜索引擎收录
												<br />
												· CDN加速，全球访问速度优化
											</p>
										</div>
									</motion.div>
								</div>

								{/* 按钮组 */}
								{/* <ButtonGroup
									buttons={["邮件营销", "应用商店", "员工管理", "开始建站" ]}
								/> */}
							</motion.div>

							{/* 右侧：手机界面展示 */}
							<motion.div 
								initial={{ opacity: 0, x: 50 }}
								whileInView={{ opacity: 1, x: 0 }}
								transition={{ duration: 0.8, delay: 0.4 }}
								viewport={{ once: true }}
								className=""
							>
								<SEOOptimizedImage
									src="https://www.shoplazza.cn/wstmart/home/<USER>/default/images/solution_4.png"
									alt="手机界面展示"
									width={1000}
									height={100}
									className="h-full w-full object-contain"
								></SEOOptimizedImage>
							</motion.div>
						</div>
					</div>
				</div>
			</section>
		</>
	);
}
