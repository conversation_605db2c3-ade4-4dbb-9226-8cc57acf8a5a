import React from "react";
import Image from "next/image";
import CountUp from 'react-countup';

const StatsItem = ({ number, label }: { number: string; label: string }) => {
  // 解析数字和后缀，如 "500+" -> end=500, suffix="+"
  const [end, suffix] = number.split(/(\D+)/).filter(Boolean);
  const num = parseInt(end, 10);

  return (
    <div className="text-center">
      <h3 className="text-4xl font-bold text-red-500 mb-2">
        <CountUp end={num} duration={2} suffix={suffix || ''} />
      </h3>
      <p className="text-gray-600 text-sm">
        {label}
      </p>
    </div>
  );
};

const StatsSection = () => {
  const stats = [
    {
      number: "500+",
      label: "全球范围内500+合作伙伴"
    },
    {
      number: "10亿+",
      label: "每日超过10亿次独立访问量"
    },
    {
      number: "100万+",
      label: "每日超过100万次商户触发行为"
    },
    {
      number: "180+",
      label: "覆盖全球180多个国家/地区"
    }
  ];

  return (
    <section className=" bg-[#f8f4ec]">
      <div className="container mx-auto px-4 mb-[60px]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => {
            const [end, suffix] = stat.number.split(/(\D+)/).filter(Boolean);
            const num = parseInt(end, 10);
            return (
              <div key={index} className="text-center">
                <h3 className="md:text-3xl text-2xl font-bold text-[#FF0000] mb-2">
                  <CountUp
                    className="md:text-[32px] text-[22px] font-semibold"
                    end={num}
                    duration={2}
                    suffix={suffix || ''}
                    enableScrollSpy={true}
                    scrollSpyOnce={true} />
                </h3>
                <p className="text-[#222222] md:text-base text-[14px]">
                  {stat.label}
                </p>
              </div>
            );
          })}
        </div>
      </div>

      {/* 大图板块 */}
      <div className="w-full h-auto relative z-[1] overflow-hidden bg-[#FFA180]">
         {/* 添加伪类效果 */}
        <div className="absolute top-0 left-0 w-full h-1/2 bg-[#f8f4ec] z-[1] pointer-events-none"></div>
        <div className="w-full max-w-[1920px] h-auto relative z-[5] mx-auto md:px-[160px] px-[24px]">
            <div className="w-full h-auto overflow-hidden">
                <Image
                    src="https://www.shoplazza.cn/upload/ztyImg/2024-04/6618e66f44dd9.png"
                    alt="Pinshop Stats"
                    width={1600}
                    height={904}
                    className="w-full h-auto object-cover  shadow-lg"
                />
            </div>
        </div>
      </div>
    </section>
  );
};

export default StatsSection;
