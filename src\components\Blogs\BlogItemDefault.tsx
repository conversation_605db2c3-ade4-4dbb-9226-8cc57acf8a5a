import React from "react";
import Image from "next/image";
import * as Icon from "@phosphor-icons/react/dist/ssr";
import { Avatar, Tag } from "antd";
import moment from "moment/moment";
import { type Blog } from "@/lib/@types/api/blog";
import { Link } from "@/navigation";
import { CalendarTwoTone } from "@ant-design/icons";
import clsx from "clsx";

interface BlogProps {
	news: Blog.BlogListItem;
	index?: number;
}

const BlogItemDefault: React.FC<BlogProps> = ({ news, index }) => {
	return (
		<>
			<Link
				href={"/blog/" + news.blog_slug}
				className="flex transform cursor-pointer flex-col overflow-hidden rounded-lg bg-white transition-all duration-700 hover:-translate-y-2 md:flex-row"
			>
				<div className="flex-shrink-0">
					<img
						alt={news.blog_title}
						className="h-[200px] w-full object-cover md:h-[280px] md:w-[380px] lg:h-[300px] lg:w-[400px]"
						src={news.blog_cover_origin}
						width="600"
					/>
				</div>
				{/* {news.blog_cover_type ? (
					<img
						alt="Person standing in a factory with machinery in the background"
						className={clsx("mb-4 h-[350px] w-full object-cover",index % 3 == 0&&'h-auto') }
						src={news.blog_cover_origin}
						width="600"
					/>
				) : (
					<video
						autoPlay
						muted
						loop
						playsInline
						className="mb-4 h-[350px] w-full object-cover"
						src={news.blog_cover_origin}
						width="600"
					/>
				)} */}

				<div className="flex flex-1 flex-col justify-between px-4 py-4 md:py-6">
					<div>
						<h3 className="mb-2 line-clamp-1 text-base font-semibold text-black md:mb-3 md:text-lg lg:text-xl">
							{news.blog_title}
						</h3>
						<p className="mb-3 line-clamp-2 text-sm font-normal text-[#677E6E] md:mb-4 md:line-clamp-3 md:text-base">
							{news.blog_excerpt}
						</p>
						<div className="flex flex-col gap-2 text-xs text-gray-500 md:mt-4 md:flex-row md:items-center md:gap-4 md:text-sm">
							<div className="flex items-center">
								<i className="ri-user-line text-lg text-[#B3BABD] md:text-xl"></i>
								<span className="ml-2 text-sm md:text-base">{process.env.NEXT_PUBLIC_COMPANY_NAME}</span>
							</div>
							<div className="flex items-center">
								<CalendarTwoTone twoToneColor="#a1a1a1" />{" "}
								<span className="ml-1 text-sm md:text-base">
									{moment(news.blog_upload_time).format("MMM DD YYYY")}
								</span>
							</div>
						</div>
					</div>

					{/* 分类信息展示 */}
					<div className="mt-3 flex flex-wrap gap-1 overflow-hidden md:mt-4 md:gap-2">
						{news.blog_classification_list &&
							news.blog_classification_list.map((item) => {
								return (
									<Tag color="" className="text-xs uppercase md:text-sm" key={item.cls_slug}>
										{item.cls_name}
									</Tag>
								);
							})}
					</div>
				</div>
			</Link>
		</>
	);
};

export default BlogItemDefault;
