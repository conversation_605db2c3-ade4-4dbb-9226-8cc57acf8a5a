"use client";
import { useRef, useEffect, useState, useMemo } from "react";
import { useLenis } from "lenis/react";

/*
 * 组件说明：
 * 1. 当组件完全进入视口时把内部内容 fixed 在视口内，利用 transform 完成横向滚动劫持；
 * 2. 横向滚动结束后，解除 fixed，恢复正常页面滚动；
 * 3. 采用 tailwindcss，移动端自动一列展示；
 * 4. 通过获取实际元素尺寸进行动态宽度计算，优化卡片展示效果；
 * 5. 集成 Lenis 平滑滚动，提供更好的用户体验；
 */

const Introduction = () => {
	const containerRef = useRef<HTMLDivElement>(null); // 外层容器，用于计算滚动区间
	const sliderRef = useRef<HTMLDivElement>(null); // 横向滑动区域
	const cardRefs = useRef<(HTMLElement | null)[]>([]); // 每个卡片的 ref
	const [progress, setProgress] = useState(0); // translateX 位移
	const [fixed, setFixed] = useState(false); // 是否 position:fixed
	const [cardDimensions, setCardDimensions] = useState({ width: 0, gap: 0 }); // 卡片尺寸
	const [isScrolling, setIsScrolling] = useState(false); // 滚动状态

	// 基于overflow-hidden容器实际宽度的卡片尺寸计算
	const calculateCardDimensions = () => {
		if (typeof window === "undefined") return { width: 350, gap: 24 };

		// 获取overflow-hidden容器的实际宽度
		const overflowContainer = sliderRef.current?.parentElement;
		if (!overflowContainer) return { width: 350, gap: 24 };

		const containerWidth = overflowContainer.getBoundingClientRect().width;
		const viewportWidth = window.innerWidth;
		const gap = 24;

		// 移动端：显示1.2个卡片
		if (viewportWidth < 768) {
			const cardWidth = containerWidth / 1.2;
			return {
				width: Math.floor(cardWidth),
				gap,
			};
		}

		// 桌面端：显示3个卡片
		const cardWidth = (containerWidth - gap * 2) / 3; // 3个卡片，2个间距

		return {
			width: Math.floor(cardWidth),
			gap,
		};
	};

	// 进度百分比 (0 - 1)，基于container实际宽度
	const progressPercent = useMemo(() => {
		if (typeof window === "undefined" || !sliderRef.current) return 0;

		// 获取overflow-hidden容器的实际宽度
		const overflowContainer = sliderRef.current.parentElement;
		if (!overflowContainer) return 0;

		const containerWidth = overflowContainer.getBoundingClientRect().width;
		const max = sliderRef.current.scrollWidth - containerWidth;
		if (max <= 0) return 0;
		return Math.min(progress / max, 1);
	}, [progress]);

	// 计算sliderRef的总宽度 - 不预留结尾空间
	const sliderWidth = useMemo(() => {
		const { width, gap } = cardDimensions;
		const cardCount = 5;

		// 基础宽度：所有卡片 + 间距（最后一张卡片不需要右边距）
		return width * cardCount + gap * (cardCount - 1);
	}, [cardDimensions]);

	// 计算卡片尺寸并更新状态
	useEffect(() => {
		const updateDimensions = () => {
			const dimensions = calculateCardDimensions();
			setCardDimensions(dimensions);
		};

		// 初始计算
		updateDimensions();

		// 监听窗口尺寸变化
		const handleResize = () => {
			updateDimensions();
		};

		window.addEventListener("resize", handleResize);
		return () => window.removeEventListener("resize", handleResize);
	}, []);

	// 设置容器高度
	useEffect(() => {
		const container = containerRef.current;
		const slider = sliderRef.current;
		if (!container || !slider) return;

		// 基于overflow-hidden容器实际宽度的高度计算
		const updateHeight = () => {
			const overflowContainer = slider.parentElement;
			if (!overflowContainer) return;

			const containerWidth = overflowContainer.getBoundingClientRect().width;
			const scrollDistance = Math.max(0, sliderWidth - containerWidth);
			const height = scrollDistance + window.innerHeight;
			container.style.height = `${Math.max(height, window.innerHeight)}px`;
		};

		// 延迟执行，确保 sliderWidth 已更新
		const timer = setTimeout(updateHeight, 100);

		// 监听窗口尺寸变化
		window.addEventListener("resize", updateHeight);

		// 使用 ResizeObserver 监听内容尺寸变化
		const resizeObserver = new ResizeObserver(updateHeight);
		resizeObserver.observe(slider);

		return () => {
			clearTimeout(timer);
			window.removeEventListener("resize", updateHeight);
			resizeObserver.disconnect();
		};
	}, [sliderWidth]);

	// 使用 Lenis 平滑滚动事件监听
	const lenis = useLenis(({ scroll, velocity }: { scroll: number; velocity: number }) => {
		const container = containerRef.current;
		const slider = sliderRef.current;
		if (!container || !slider) return;

		// 设置滚动状态，用于优化性能和视觉反馈
		setIsScrolling(Math.abs(velocity) > 0.1);

		const containerTop = container.offsetTop;

		// 获取overflow-hidden容器的实际宽度
		const overflowContainer = slider.parentElement;
		if (!overflowContainer) return;

		const containerWidth = overflowContainer.getBoundingClientRect().width;

		// 基于实际容器宽度的滚动距离计算
		const horizontalMax = Math.max(0, sliderWidth - containerWidth);
		const offsetTop = Math.min(Math.max(scroll - containerTop, 0), horizontalMax);
		setProgress(offsetTop);

		// 计算横向滚动模块完整展示所需的位置
		// 需要确保进度条也能看到，所以添加额外的缓冲区

		// 只有横向滚动模块完全展示后才开始 setFixed
		const shouldBeFixed = scroll >= containerTop && offsetTop < horizontalMax;
		setFixed(shouldBeFixed);
	});

	// 初始化滚动位置
	useEffect(() => {
		if (lenis) {
			// 执行一次初始计算
			const handleInitialScroll = () => {
				const container = containerRef.current;
				const slider = sliderRef.current;
				if (!container || !slider) return;

				const scroll = lenis.scroll;
				const containerTop = container.offsetTop;
				const overflowContainer = slider.parentElement;
				if (!overflowContainer) return;

				const containerWidth = overflowContainer.getBoundingClientRect().width;
				const horizontalMax = Math.max(0, sliderWidth - containerWidth);
				const offsetTop = Math.min(Math.max(scroll - containerTop, 0), horizontalMax);
				setProgress(offsetTop);

				const shouldBeFixed = scroll >= containerTop && offsetTop < horizontalMax;
				setFixed(shouldBeFixed);
			};

			handleInitialScroll();
		}
	}, [lenis, sliderWidth]);

	return (
		<section ref={containerRef} className="relative box-border bg-[#ffa180]">
			{/* 根据 fixed 状态切换定位 */}
			<div
				className={`${
					fixed ? "fixed top-0 z-10" : "relative"
				} w-full overflow-hidden bg-[#ffa180] pb-[40px] pt-[120px]`}
				style={{
					transform: `translateY(${fixed ? 0 : progress}px)`,
				}}
			>
				<div className="container mx-auto px-4 md:px-8">
					<h2 className="text-center text-3xl font-bold md:text-[40px]">五步建站-快速开启售卖</h2>

					{/* 视频区域 */}
					<video
						src="/image/product/open_shop.mp4"
						autoPlay
						muted
						loop
						className="mt-16 h-[600px] w-full object-cover shadow-lg"
					></video>

					{/* 步骤横向滑动 */}
					<div className="overflow-hidden">
						<div
							ref={sliderRef}
							className={`mt-10 flex ${isScrolling ? "will-change-transform" : ""}`}
							style={{
								transform: `translateX(-${progress}px)`,
								transition: isScrolling ? "none" : "transform 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
								width: `${sliderWidth}px`,
							}}
						>
							{/* 第一步：添加商品 */}
							<article
								ref={(el) => (cardRefs.current[0] = el)}
								className="flex flex-col"
								style={{
									width: `${cardDimensions.width}px`,
									marginRight: `${cardDimensions.gap}px`,
									flexShrink: 0,
								}}
							>
								<div className="flex items-start justify-between">
									<div className="flex-1">
										<div className="mb-4">
											<svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60">
												<circle cx="30" cy="30" r="30" fill="#FF0000"></circle>
												<path
													d="M18.9583 26.4056C19.0771 25.3987 19.9306 24.64 20.9445 24.64H39.0555C40.0694 24.64 40.9229 25.3987 41.0417 26.4056L42.7363 40.7656C42.8767 41.9548 41.9476 43 40.7501 43H19.2499C18.0524 43 17.1233 41.9548 17.2637 40.7656L18.9583 26.4056ZM23.5 32.2H25.6667V27.88H23.5V32.2ZM34.3333 32.2H36.5V27.88H34.3333V32.2ZM25.6667 22.48H23.5C23.5455 20.6354 24.1782 19.1039 25.3958 17.89C26.6135 16.6761 28.1475 16.0454 30 16C31.8503 16.0454 33.3865 16.6761 34.6042 17.89C35.8218 19.1039 36.4545 20.6332 36.5 22.48H34.3333C34.3117 21.2639 33.887 20.2466 33.0637 19.4258C32.2403 18.605 31.2177 18.1838 30 18.16C28.7802 18.1816 27.7597 18.605 26.9363 19.4258C26.113 20.2466 25.6905 21.2661 25.6667 22.48Z"
													fill="white"
												></path>
											</svg>
										</div>
										<h3 className="text-[24px] md:text-xl">添加商品</h3>
									</div>
									<span className="ml-4 text-5xl font-bold text-[#fff] opacity-20 md:text-[144px]">01</span>
								</div>
								<p className="mt-3 leading-relaxed">
									品店支持一键搬迁、批量上传、商品采集等方式快速上传商品，提高运营效率。
								</p>
							</article>

							{/* 第二步：设置店铺 */}
							<article
								ref={(el) => (cardRefs.current[1] = el)}
								className="flex flex-col"
								style={{
									width: `${cardDimensions.width}px`,
									marginRight: `${cardDimensions.gap}px`,
									flexShrink: 0,
								}}
							>
								<div className="flex items-start justify-between">
									<div className="flex-1">
										<div className="mb-4">
											<svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60">
												<circle cx="30" cy="30" r="30" fill="#FF0000"></circle>
												<path
													d="M30 15C21.716 15 15 21.716 15 30C15 38.284 21.716 45 30 45C38.284 45 45 38.284 45 30C45 21.716 38.284 15 30 15ZM30 42C23.373 42 18 36.627 18 30C18 23.373 23.373 18 30 18C36.627 18 42 23.373 42 30C42 36.627 36.627 42 30 42ZM30 22C26.686 22 24 24.686 24 28C24 31.314 26.686 34 30 34C33.314 34 36 31.314 36 28C36 24.686 33.314 22 30 22Z"
													fill="white"
												></path>
											</svg>
										</div>
										<h3 className="text-[24px] md:text-xl">设置店铺</h3>
									</div>
									<span className="ml-4 text-5xl font-bold text-[#fff] opacity-20 md:text-[144px]">02</span>
								</div>
								<p className="mt-3 leading-relaxed">
									快速配置店铺信息，包括店铺名称、Logo、联系方式等基本信息。
								</p>
							</article>

							{/* 第三步：选择主题 */}
							<article
								ref={(el) => (cardRefs.current[2] = el)}
								className="flex flex-col"
								style={{
									width: `${cardDimensions.width}px`,
									marginRight: `${cardDimensions.gap}px`,
									flexShrink: 0,
								}}
							>
								<div className="flex items-start justify-between">
									<div className="flex-1">
										<div className="mb-4">
											<svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60">
												<circle cx="30" cy="30" r="30" fill="#FF0000"></circle>
												<path
													d="M30 15C21.716 15 15 21.716 15 30C15 38.284 21.716 45 30 45C38.284 45 45 38.284 45 30C45 21.716 38.284 15 30 15ZM30 42C23.373 42 18 36.627 18 30C18 23.373 23.373 18 30 18C36.627 18 42 23.373 42 30C42 36.627 36.627 42 30 42ZM30 22C26.686 22 24 24.686 24 28C24 31.314 26.686 34 30 34C33.314 34 36 31.314 36 28C36 24.686 33.314 22 30 22Z"
													fill="white"
												></path>
											</svg>
										</div>
										<h3 className="text-[24px] md:text-xl">选择主题</h3>
									</div>
									<span className="ml-4 text-5xl font-bold text-[#fff] opacity-20 md:text-[144px]">03</span>
								</div>
								<p className="mt-3 leading-relaxed">
									从丰富的主题模板中选择适合的店铺风格，一键应用快速美化。
								</p>
							</article>

							{/* 第四步：配置支付 */}
							<article
								ref={(el) => (cardRefs.current[3] = el)}
								className="flex flex-col"
								style={{
									width: `${cardDimensions.width}px`,
									marginRight: `${cardDimensions.gap}px`,
									flexShrink: 0,
								}}
							>
								<div className="flex items-start justify-between">
									<div className="flex-1">
										<div className="mb-4">
											<svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60">
												<circle cx="30" cy="30" r="30" fill="#FF0000"></circle>
												<path
													d="M45 20H15C13.895 20 13 20.895 13 22V38C13 39.105 13.895 40 15 40H45C46.105 40 47 39.105 47 38V22C47 20.895 46.105 20 45 20ZM45 38H15V22H45V38ZM20 25H40V27H20V25ZM20 30H35V32H20V30Z"
													fill="white"
												></path>
											</svg>
										</div>
										<h3 className="text-[24px] md:text-xl">配置支付</h3>
									</div>
									<span className="ml-4 text-5xl font-bold text-[#fff] opacity-20 md:text-[144px]">04</span>
								</div>
								<p className="mt-3 leading-relaxed">
									集成多种支付方式，支持信用卡、PayPal、支付宝等主流支付渠道。
								</p>
							</article>

							{/* 第五步：开始销售 */}
							<article
								ref={(el) => (cardRefs.current[4] = el)}
								className="flex flex-col"
								style={{
									width: `${cardDimensions.width}px`,
									marginRight: "0", // 最后一张卡片不需要右边距，结尾空间在sliderWidth中处理
									flexShrink: 0,
								}}
							>
								<div className="flex items-start justify-between">
									<div className="flex h-full flex-1 flex-col justify-between">
										<div>
											<svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60">
												<circle cx="30" cy="30" r="30" fill="#FF0000"></circle>
												<path
													d="M30 15C21.716 15 15 21.716 15 30C15 38.284 21.716 45 30 45C38.284 45 45 38.284 45 30C45 21.716 38.284 15 30 15ZM30 42C23.373 42 18 36.627 18 30C18 23.373 23.373 18 30 18C36.627 18 42 23.373 42 30C42 36.627 36.627 42 30 42ZM30 22C26.686 22 24 24.686 24 28C24 31.314 26.686 34 30 34C33.314 34 36 31.314 36 28C36 24.686 33.314 22 30 22Z"
													fill="white"
												></path>
											</svg>
										</div>
										<h3 className="pb-1 text-[24px] md:text-xl">开始销售</h3>
									</div>
									<span className="ml-4 text-5xl font-bold text-[#fff] opacity-20 md:text-[144px]">05</span>
								</div>
								<p className="mt-3 leading-relaxed">
									完成所有配置后，您的店铺就可以开始接受订单，开启跨境电商之旅。
								</p>
							</article>
						</div>

						{/* 进度条 */}
						<div className="mt-4 h-1 w-full rounded-full bg-[#ffffff10] md:mt-8">
							<div
								className={`h-full rounded-full bg-[#ff0000] ${isScrolling ? "will-change-transform" : ""}`}
								style={{
									width: `${progressPercent * 100}%`,
									transition: isScrolling ? "none" : "width 0.1s cubic-bezier(0.25, 0.46, 0.45, 0.94)",
								}}
							/>
						</div>
					</div>
				</div>
			</div>
		</section>
	);
};

export default Introduction;
