"use client";
import React, { useState } from "react";
import PricingPlans from "./PricingPlans";
import useMenuMobile from "@/store/useMenuMobile";
import { useModalInquiryContext } from "@/context/ModalInquiryContext";
import FAQ from "../Solution/FAQ";
import SolutionFooter from "../Solution/SolutionFooter";
import HomeReview from "../ZC/HomeReview";
import { useHeaderHeight } from "@/lib/hooks/useHeaderHeight";

export default function Index() {
	const { openMenuMobile, handleMenuMobile } = useMenuMobile();
	const { openModalInquiry } = useModalInquiryContext();
	const paddingTop = useHeaderHeight();
	// 登录模态框状态管理
	const [openLoginModal, setOpenLoginModal] = useState(false);
	const [activeId, setActiveId] = useState(1); // 1: 登录, 2: 注册
	//     // 处理登录按钮点击
	const handleLoginClick = () => {
		setActiveId(1); // 设置为登录面板
		setOpenLoginModal(true);
		// 只有在移动端菜单打开时才关闭它
		if (openMenuMobile) {
			handleMenuMobile();
		}
	};

	return (
		<div style={{ paddingTop }}>
			<PricingPlans />
			<FAQ onLoginClick={handleLoginClick} />
      <HomeReview />
       <SolutionFooter />
		</div>
	);
}
