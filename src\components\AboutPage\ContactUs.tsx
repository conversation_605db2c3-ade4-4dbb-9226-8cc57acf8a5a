import React, { useState } from 'react';


export default function ContactUs() {
    const [hoveredIndex, setHoveredIndex] = useState<number | null>(null); // 管理鼠标移入项的索引

    const locations = [
        {
            title: '中国总部',
            address: '广东省深圳市南山区科技园北区朗山路 13 号清华紫光科技园 3 层',
            position: { lat: 22.5431, lng: 114.0579 }, // 示例坐标（深圳）
        },
        {
            title: '加拿大总部',
            address: '123 Example Street, Vancouver, BC, Canada',
            position: { lat: 49.2827, lng: -123.1207 }, // 示例坐标（温哥华）
        },
    ];

    const mapStyles = {
        height: '400px', // 地图高度（可调整）
        width: '100%',
    };

    return (
        <section className="bg-[#f8f4ec] md:py-[120px] py-[60px]">
            <div className="max-w-[1920px] mx-auto md:px-[160px] px-4">
                {/* 移动端布局：地图在上，内容在下 */}
                <div className="md:hidden">
                    <div className="w-full mb-8">
                        <iframe
                            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3684.058!2d114.0579!3d22.5431!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjLCsDMyJzQzLjgiTiAxMTTCsDAzJzI4LjQiRQ!5e0!3m2!1sen!2sus!4v1690000000000"
                            width="100%"
                            height="300" // 移动端高度减小
                            style={{ border: 0 }}
                            loading="lazy"
                            referrerPolicy="no-referrer-when-downgrade"
                        ></iframe>
                    </div>
                    
                    <div className="w-full">
                        <h2 className="text-3xl font-bold text-[#222222] mb-6">联系我们</h2>
                        <ul className="space-y-4">
                            {locations.map((location, index) => (
                                <li
                                    key={index}
                                    className="border-t border-gray-300 pt-4 relative"
                                >
                                    <div className="flex justify-between items-center cursor-pointer">
                                        <span className="text-lg font-bold text-gray-700">{location.title}</span>
                                        <span className="text-2xl text-gray-500">+</span>
                                    </div>
                                    <div className="mt-2 text-gray-600">
                                        {location.address}
                                    </div>
                                </li>
                            ))}
                        </ul>
                    </div>
                </div>
                
                {/* PC端布局：左右排列 */}
                <div className="hidden md:flex items-start justify-between gap-20">
                    {/* 左侧 Google 地图 */}
                    <div className="w-1/2">
                        <iframe
                            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3684.058!2d114.0579!3d22.5431!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjLCsDMyJzQzLjgiTiAxMTTCsDAzJzI4LjQiRQ!5e0!3m2!1sen!2sus!4v1690000000000"
                            width="100%"
                            height="500"
                            style={{ border: 0 }}
                            loading="lazy"
                            referrerPolicy="no-referrer-when-downgrade"
                        ></iframe>
                    </div>

                    {/* 右侧标题 + UL 列表 */}
                    <div className="w-1/2">
                        <h2 className="text-[40px] font-bold text-[#222222] mb-6">联系我们</h2>
                        <ul className="space-y-4">
                            {locations.map((location, index) => (
                                <li
                                    key={index}
                                    className="border-t border-gray-300 pt-4 relative"
                                    onMouseEnter={() => setHoveredIndex(index)}
                                    onMouseLeave={() => setHoveredIndex(null)}
                                >
                                    <div className="flex justify-between items-center cursor-pointer">
                                        <span className="text-lg font-bold text-gray-700">{location.title}</span>
                                        <span className="text-2xl text-gray-500">+</span>
                                    </div>
                                    {/* 鼠标移入时显示地址，带过渡效果 */}
                                    <div
                                        className={`mt-2 text-gray-600 transition-all duration-300 ease-in-out overflow-hidden ${hoveredIndex === index ? 'max-h-20 opacity-100' : 'max-h-0 opacity-0'
                                            }`}
                                    >
                                        {location.address}
                                    </div>
                                </li>
                            ))}
                        </ul>
                    </div>
                </div>
            </div>
        </section>
    );
}