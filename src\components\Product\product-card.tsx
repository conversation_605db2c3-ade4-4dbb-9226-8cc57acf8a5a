"use client";
import React, { useEffect, useState } from "react";
import { Link } from "@/navigation";
import { useTranslations } from "next-intl";
import { defaultLocale } from "@/config";
import { Button } from "../Button";
import { Placeholder } from "../Placeholder";
import { ProductListItemFragment } from "@/gql/graphql";
import { useLoveStore } from "@/lib/store/love.store";
import clsx from "clsx";
import Svg from "../Header/Menu/Svg";
import Lightbox from "react-image-lightbox";
import "react-image-lightbox/style.css"; // 需要引入样式
import { motion } from "framer-motion";
import { containerVariants, itemVariants, productTranslationName } from "@/lib/utils/util";
import { Tooltip } from "antd";
import { useCompareStore } from "@/lib/store/Compare.store";
import CompareList from "../CompareList";
import Image from "next/image";
import { BodyText, handlerInnerHtml } from "../BodyText";
export type ImgObj = {
	id?: number;
	url: string;
	name: string;
	type: string;
};
export type Metadata = { __typename?: "MetadataItem" | undefined; key: string; value: string };
export const filterCateImg = (metadataList: Metadata[]) => {
	let imgUrlList: ImgObj[] = [];
	if (metadataList && metadataList.length > 0) {
		const media = metadataList.filter((i: Metadata) => i.key === "media");
		if (media.length > 0) imgUrlList = JSON.parse(media[0].value) as ImgObj[];
	}
	return imgUrlList;
};
type dscObj = {
	data: { text: string };
	type: string;
};
type dscList = {
	blocks: dscObj[];
};

// 动画配置
const containerHoverVariants = {
	hidden: { opacity: 0 },
	visible: {
		opacity: 1,
		transition: {
			staggerChildren: 0.1, // 子元素依次延迟显示
			delayChildren: 0.2, // 动画整体延迟
		},
	},
};

const iconVariants = {
	hidden: { opacity: 0, y: 20 },
	visible: { opacity: 1, y: 0 },
};

export const filterSortDsc = (dsc: string) => {
	let sortDsc = "";
	let longDsc = "";
	if (dsc) {
		const dscObj = JSON.parse(dsc) as dscList;

		if (dscObj && dscObj.blocks.length > 0) {
			sortDsc = dscObj.blocks[1].data.text;
			longDsc = dscObj.blocks[0].data.text;
		}
	}

	return { sortDsc, longDsc };
};
export default function ProductCard({
	productItem,
	locale,
}: {
	productItem: ProductListItemFragment;
	locale: string;
}) {
	const [isOpen, setIsOpen] = useState(false);
	const [imageUrl, setimageUrl] = useState(""); // 替换为你的图片 URL
	let { compareIds, show, changeShow, setcCmpareProducts } = useCompareStore();
	const t = useTranslations();

	const pt = productItem.media;
	const imgs =  JSON.parse(pt) || [];
	const [currentImageIndex, setCurrentImageIndex] = useState(0);

	const [isHovered, setIsHovered] = useState(false); // 控制动画容器状态
	const handleMouseEnter = () => {
		setIsHovered(true);
		setCurrentImageIndex(1); // 切换到第二张图片
	};
	const handleMouseLeave = () => {
		setIsHovered(false);
		setCurrentImageIndex(0); // 切换回第一张图片
	};

	// 添加对比
	const addOrDelCompare = (event: React.MouseEvent, product) => {
		event.stopPropagation();
		console.log(product, "product");

		const id = product?.id;
		if (id) {
			setcCmpareProducts(id);
			changeShow(true);
		}
	};
	let html =
		locale == defaultLocale
			? filterSortDsc(productItem?.descriptionJson).sortDsc
			: filterSortDsc(productItem?.translation?.descriptionJson).sortDsc ||
				filterSortDsc(productItem?.descriptionJson).sortDsc;
     let ProductName=locale === defaultLocale
							? productTranslationName(productItem.name)
							: productTranslationName(productItem.translation)
								? productTranslationName(productItem.translation.name)
								: productTranslationName(productItem.name)
	return (
		<div
			onMouseEnter={handleMouseEnter}
			onMouseLeave={handleMouseLeave}
			className="group relative flex  flex-col  overflow-hidden border bg-white transition-all duration-500 hover:-translate-y-1 hover:shadow-[0_10px_40px_rgb(0,0,0,0.12)] max-md:border-none"
		>
			<div className="relative">
				<div className="relative  overflow-hidden max-md:bg-gray-50 ">
					<Link href={"/product/" + productItem.slug} className="md:block md:h-full md:w-full">
						<div className="max-md:!hidden md:!block">
							{currentImageIndex == 0 ? (
								<Image
									width={800}
									height={900}
									quality={70}
									alt={ProductName}
									className=" h-full w-full object-cover transition-all duration-700 ease-in-out will-change-transform group-hover:scale-110"
									src={imgs[0]?.url || "/image/default-image.webp"}
								/>
							) : (
								<Image
									width={800}
									height={900}
									quality={70}
									alt={ProductName}
									className=" h-full w-full object-cover transition-all duration-700 ease-in-out will-change-transform group-hover:scale-110"
									src={imgs[1]?.url || "/image/default-image.webp"}
								/>
							)}
						</div>

						<img
							className="!hidden w-full object-center max-md:!block"
							src={imgs[currentImageIndex]?.url || "/image/default-image.webp"}
							alt={ProductName}
						/>
						<div className="absolute inset-0 bg-gradient-to-t from-black/30 via-black/5 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
					</Link>
					{/* 动画容器 */}
					<motion.div
						className="absolute bottom-[10px] left-0 flex w-full items-center justify-center gap-x-3"
						initial="hidden"
						animate={isHovered ? "visible" : "hidden"} // 动画状态绑定到 isHovered
						variants={containerHoverVariants}
					>
						{/* //爱心 */}
						<Tooltip title={t("nav.Like")}>
							<motion.span
								className="boxShadow flex h-[42px] w-[42px] cursor-pointer items-center justify-center rounded-sm bg-white "
								variants={iconVariants} // 子元素动画
							>
								<IconLove product={productItem} />
							</motion.span>
						</Tooltip>

						{/* //对比 */}
						<div className="max-md:hidden">
							<Tooltip title={t("nav.Add to Compare")}>
								<motion.span
									className="boxShadow flex h-[42px] w-[42px] cursor-pointer items-center justify-center rounded-sm bg-white "
									variants={iconVariants} // 子元素动画
								>
									<div onClick={(e) => addOrDelCompare(e, productItem)}>
										{compareIds.includes(productItem.id) ? (
											<i className={clsx("ri-check-line ri-xl")}></i>
										) : (
											<Svg isScrolled={isHovered} />
										)}
									</div>
								</motion.span>
							</Tooltip>
						</div>

						{/* //查看 */}
						<Tooltip title={t("nav.Quick View")}>
							<motion.span
								className="boxShadow flex h-[42px] w-[42px] cursor-pointer items-center justify-center rounded-sm bg-white "
								variants={iconVariants} // 子元素动画
							>
								<i
									className={clsx("ri-eye-line ri-xl")}
									onClick={() => {
										setimageUrl(imgs[currentImageIndex]?.url || "/image/default-image.webp");
										setIsOpen(true);
									}}
								></i>
							</motion.span>
						</Tooltip>
					</motion.div>
				</div>
			</div>

			<div className="flex flex-1 flex-col gap-4 p-5 max-md:gap-1 max-md:p-3">
				<div className="flex items-start justify-between gap-3">
					<Link
						href={"/product/" + productItem.slug}
						className=" line-clamp-1 flex-1 text-sm !text-black transition-colors  duration-300 hover:text-black group-hover:text-black max-md:line-clamp-2 max-md:!text-sm"
					>
						{ProductName}
					</Link>
				</div>
				{renderPrice(productItem.pricing?.priceRange)}
        
       {/* 简短描述 */}
				{/* <div className="h-[3em] overflow-hidden">
					<p
						className="line-clamp-2 text-sm leading-relaxed text-gray-600/90 "
						dangerouslySetInnerHTML={{ __html: handlerInnerHtml(html) }}
					></p>
				</div> */}

				{/* <div className="mt-auto pt-4 max-md:hidden">
                    <Link href={`/product/${productItem.slug}`} className="block w-full">
                        <Button
                            size="sm"
                            className="group/btn bg-primary-600 hover:bg-primary-700 relative w-full overflow-hidden rounded-xl px-6 py-3.5 text-sm font-medium text-white shadow-[0_2px_8px_rgb(0,0,0,0.08)] transition-all duration-300 hover:shadow-[0_4px_12px_rgb(var(--primary-600-rgb)/0.3)]"
                        >
                            <span className="relative z-10 flex items-center justify-center gap-2">
                                <span className="transition-transform duration-300 group-hover/btn:translate-x-[-4px]">
                {t("base.viewMore")}
                                </span>
                                <i className="ri-arrow-right-line transition-all duration-300 group-hover/btn:translate-x-1" />
                            </span>
                        </Button>
                    </Link>
                </div> */}
			</div>
			{isOpen && <Lightbox mainSrc={imageUrl} onCloseRequest={() => setIsOpen(false)} />}
		</div>
	);
}

export function IconLove(props: { product?: any }) {
	let product = props.product;

	const [isLove, setIsLove] = useState(false);
	const { loveIds, setLoveProducts } = useLoveStore();
	const t = useTranslations();

	// console.log(loveIds,'product');
	useEffect(() => {
		if (loveIds.length && product?.id) {
			setIsLove(loveIds.includes(product.id));
		} else {
			setIsLove(false);
		}
	}, [loveIds]);
	const addOrDelLike = (event: React.MouseEvent) => {
		event.stopPropagation(); //
		const id = product?.id;
		if (id) {
			setLoveProducts(id);
		}
	};
	return (
		<i
			onClick={addOrDelLike}
			className={clsx(
				" text-xl text-[#000]",
				isLove ? " ri-heart-fill !text-[#d53a3d]" : "ri-heart-line !text-[#000]",
			)}
		></i>
	);
}
export function IconCompare(props: { product?: any }) {
	let product = props.product;

	const [isCompare, setIsCompare] = useState(false);
	const { compareIds, setcCmpareProducts, changeShow } = useCompareStore();
	const t = useTranslations();

	// console.log(loveIds,'product');
	useEffect(() => {
		if (compareIds.length && product?.id) {
			setIsCompare(compareIds.includes(product.id));
		} else {
			setIsCompare(false);
		}
	}, [compareIds]);
	// 添加对比
	const addOrDelCompare = (event: React.MouseEvent, product) => {
		event.stopPropagation();

		const id = product?.id;
		if (id) {
			setcCmpareProducts(id);
		}

		changeShow(true);
	};
	return (
		<i
			onClick={(e) => addOrDelCompare(e, product)}
			className={clsx(
				" text-xl text-[#000]",
				isCompare ? " ri-delete-bin-fill" : "ri-heart-line !text-[#000]",
			)}
		></i>
	);
}

const renderPrice = (price) => {
	if (!price?.start || !price?.stop) return null;
	return (
		<div className="flex items-center space-x-2 max-md:flex-col max-md:items-start max-md:space-x-0">
			<span className="text-gray-400 line-through max-md:hidden">
      {price.stop.gross.currency} {price.stop.gross.amount}
			</span>
			<span className="font-medium text-red-600">
        {price.start.gross.currency} {price.start.gross.amount}
			</span>
		</div>
	);
};
