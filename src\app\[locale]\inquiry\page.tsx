import React from 'react'

import { MyPageProps } from '@/lib/@types/base';
import { getBasePageSeo } from '@/lib/api/seo';
import { generateSeo } from '@/lib/utils/seo';
import { Metadata } from 'next';
import { locales } from '@/config';
import dynamic from 'next/dynamic';
const InquiryData = dynamic(() => import('@/components/Inquiry/InquiryData'), {
  ssr: false
 })

export async function generateStaticParams() {
  return locales.map((locale) => ({
    locale,
  }));
} 
/**
 * 生成页面的元数据
 * @param props 页面的属性，用于生成SEO信息
 * @returns 返回页面的元数据，包括SEO信息
 */
export const generateMetadata = async (props: MyPageProps): Promise<Metadata> => {
	props.params.page = ["inquiry"];
	// 获取基础页面的SEO数据
	const seo = await getBasePageSeo(props);
	// 生成最终的SEO信息，并返回
	return generateSeo(props, {
		...seo,
		ogType: "website",
	});
};
export default function Page() {

  return (
    <div className='container min-h-[70vh]'>
        <InquiryData />
    </div>
  )
}
