"use client";
import React, { useEffect, useState } from "react";
import { Input, Button, message } from "antd";
import Image from "next/image";
import { Link } from "@/navigation";
import { <PERSON>edin<PERSON>ogo, TiktokLogo } from "@phosphor-icons/react";
import { useCategory } from "@/context/CategoriesContext";
import FollowUs from "@/components/Social/FollowUs";
import { useLocale, useTranslations } from "next-intl";
const { Search } = Input;
import { Menu, menus } from "@/lib/menu";
import { contactInfo, payarr } from "@/lib/contacts";
import Follow from "../Social/Follow";
import { useModalCustomerContext } from "@/context/CustomerContext";
import SliderBar from "../SliderBar";
import { subscribeEmail } from "@/lib/api/subscribe";
import { defaultLocale } from "@/config";
import clsx from "clsx";
import Collapse from "../Collapse";
import Indicator from "../Indicator";
import useIsMobile from "@/lib/hooks/useIsMobile";
import Translate from "../Translate";
const Footer = ({ categories }) => {
	let locale = useLocale();
	//是否展开
	const [isOpen, setIsOpen] = useState({
		isQuickIinks: true,
		isProducts: true,
		isResources: true,
		isSupport: true,
		isEmail: true,
	});
	const t = useTranslations();
	const [subscribeLoading, setSubscribeLoading] = useState(false);

	let isMobile = useIsMobile();
	useEffect(() => {
		if (isMobile) {
			setIsOpen({
				isQuickIinks: false,
				isProducts: false,
				isResources: false,
				isSupport: false,
				isEmail: false,
			});
		} else {
			setIsOpen({
				isQuickIinks: true,
				isProducts: true,
				isResources: true,
				isSupport: true,
				isEmail: true,
			});
		}
	}, [isMobile]);

	const subscribe = async (value: string) => {
		try {
			if (!value) return;
			setSubscribeLoading(true);
			await subscribeEmail({ email: value, subscribe_type: 3 });
			message.success(t("message.subscribeSuccess"));
		} catch (e) {
			message.error(e.message);
		}
		setSubscribeLoading(false);
	};

	const navigation = {
		QUICKLINKS: [
			{ id: 1, name: "menu.home", link: "/", hasSlug: true, show: true },
			{ id: 20, name: "menu.product", link: "/products", hasSlug: true, show: true },
			{ id: 3, name: "menu.blog", link: "/blog", hasSlug: true, show: true },
			{ id: 4, name: "menu.aboutUs", link: "/about-us", hasSlug: true, show: true },
			{ id: 5, name: "menu.contactUs", link: "/contact-us", hasSlug: true, show: true },
		],
		RESOURCES: [
			{ id: 7, name: "menu.resources", link: "/resources", hasSlug: true, show: true },
			{ id: 8, name: "menu.guides", link: "/guides", hasSlug: true, show: true },
			{ id: 9, name: "menu.tutorials", link: "/tutorials", hasSlug: true, show: true },
			{ id: 10, name: "menu.documentation", link: "/docs", hasSlug: true, show: true },
		],
		SUPPORT: [
			{ id: 11, name: "menu.helpCenter", link: "/help", hasSlug: true, show: true },
			{ id: 12, name: "menu.customerService", link: "/support", hasSlug: true, show: true },
			{ id: 13, name: "menu.feedback", link: "/feedback", hasSlug: true, show: true },
			{ id: 14, name: "menu.privacy", link: "/privacy", hasSlug: true, show: true },
		],
		contact: [
			{
				name: t(`menu.Address`),
				href: `https://www.google.com/maps/search/No.+409+Guanbang+International,+Linhe+Road,+Huadu+District,+Guangzhou/@23.4304758,113.1334701,12z/data=!3m1!4b1?entry=ttu&g_ep=EgoyMDI0MTExOS4yIKXMDSoASAFQAw%3D%3D`,
				value: `${contactInfo.address}`,
			},
			{
				name: ``,
				href: ``,
				value: `${contactInfo.name}`,
			},
			{
				name: t(`menu.Phone`),
				href: `tel:${contactInfo.phone}`,
				value: `${contactInfo.phone}`,
			},
			{
				name: t(`menu.Emall`),
				href: `mailto:${contactInfo.email}`,
				value: `${contactInfo.email}`,
			},
		],
	};

	return (
		<footer className="footer bg-white text-black">
			<div className="container grid grid-cols-6 gap-8 px-4 py-[60px] text-[14px] max-xl:grid-cols-3 max-md:grid-cols-1 max-md:py-[40px]">
				{/* Pinshop 公司信息 */}
				<div className="space-y-4">
					<h3 className="text-[14px] text-[#4C5D66] mb-6">品店科技</h3>
					<ul className="space-y-3">
						<li><Link href="/about" className="text-black hover:text-gray-900 transition-colors">{t("menu.aboutUs") || "公司介绍"}</Link></li>
						<li><Link href="/customers" className="text-black hover:text-gray-900 transition-colors">{t("menu.customers") || "客户案例"}</Link></li>
						<li><Link href="/blog" className="text-black hover:text-gray-900 transition-colors">{t("menu.blog") || "博客"}</Link></li>
						<li><Link href="/partners" className="text-black hover:text-gray-900 transition-colors">{t("menu.partners") || "合作伙伴"}</Link></li>
						<li><Link href="/careers" className="text-black hover:text-gray-900 transition-colors">{t("menu.careers") || "商业合作人计划"}</Link></li>
						<li><Link href="/marketplace" className="text-black hover:text-gray-900 transition-colors">{t("menu.marketplace") || "商业联盟分销"}</Link></li>
						<li><Link href="/awards" className="text-black hover:text-gray-900 transition-colors">Pinshop Awards 2024</Link></li>
					</ul>
				</div>

				{/* 产品 */}
				<div>
					<div className="max-md:flex max-md:justify-between max-md:border-b max-md:border-gray-300 max-md:pb-3">
						<h3 className="mb-6 text-[14px]  text-[#4C5D66] max-md:mb-0">{t("menu.products") || "产品"}</h3>
						<i
							className={clsx(
								"hidden text-xl max-md:block cursor-pointer transition-transform duration-200 text-black",
								!isOpen.isQuickIinks ? "ri-add-line" : "ri-subtract-fill",
							)}
							onClick={() =>
								setIsOpen((prevState) => ({ ...prevState, isQuickIinks: !prevState.isQuickIinks }))
							}
						></i>
					</div>

					<Collapse open={isOpen.isQuickIinks}>
						<ul className="space-y-3">
							<li><Link href="/themes" className="text-black hover:text-gray-900 transition-colors">{t("menu.themes") || "模版建站"}</Link></li>
							<li><Link href="/migration" className="text-black hover:text-gray-900 transition-colors">{t("menu.migration") || "一键迁移"}</Link></li>
							<li><Link href="/social" className="text-black hover:text-gray-900 transition-colors">{t("menu.social") || "社媒营销"}</Link></li>
							<li><Link href="/inventory" className="text-black hover:text-gray-900 transition-colors">{t("menu.inventory") || "库存管理"}</Link></li>
							<li><Link href="/seo" className="text-black hover:text-gray-900 transition-colors">{t("menu.seo") || "SEO优化"}</Link></li>
							<li><Link href="/email" className="text-black hover:text-gray-900 transition-colors">{t("menu.email") || "邮件营销"}</Link></li>
						</ul>
					</Collapse>
				</div>

				{/* 解决方案 */}
				<div>
					<div className="max-md:flex max-md:justify-between max-md:border-b max-md:border-gray-300 max-md:pb-3">
						<h3 className="mb-6 text-[14px]  text-[#4C5D66]  max-md:mb-0">{t("menu.solutions") || "解决方案"}</h3>
						<i
							className={clsx(
								"hidden text-xl max-md:block cursor-pointer transition-transform duration-200 text-black",
								!isOpen.isProducts ? "ri-add-line" : "ri-subtract-fill",
							)}
							onClick={() => setIsOpen((prevState) => ({ ...prevState, isProducts: !prevState.isProducts }))}
						></i>
					</div>

					<Collapse open={isOpen.isProducts}>
						<ul className="space-y-3">
							<li><Link href="/newbie" className="text-black hover:text-gray-900 transition-colors">{t("menu.newbie") || "新手建站"}</Link></li>
							<li><Link href="/dropshipping" className="text-black hover:text-gray-900 transition-colors">{t("menu.dropshipping") || "一件代发"}</Link></li>
							<li><Link href="/north-america" className="text-black hover:text-gray-900 transition-colors">{t("menu.northAmerica") || "北美出海"}</Link></li>
							<li><Link href="/pod" className="text-black hover:text-gray-900 transition-colors">{t("menu.pod") || "POD商品定制"}</Link></li>
							<li><Link href="/europe" className="text-black hover:text-gray-900 transition-colors">{t("menu.europe") || "欧洲开店"}</Link></li>
							<li><Link href="/cod" className="text-black hover:text-gray-900 transition-colors">{t("menu.cod") || "COD货到付款"}</Link></li>
							<li><Link href="/japan" className="text-black hover:text-gray-900 transition-colors">{t("menu.japan") || "日本开店"}</Link></li>
							<li><Link href="/dtc" className="text-black hover:text-gray-900 transition-colors">{t("menu.dtc") || "DTC模式"}</Link></li>
						</ul>
					</Collapse>
				</div>

				{/* 客户支持 */}
				<div>
					<div className="max-md:flex max-md:justify-between max-md:border-b max-md:border-gray-300 max-md:pb-3">
						<h3 className="mb-6 text-[14px]  text-[#4C5D66] max-md:mb-0">{t("menu.support") || "客户支持"}</h3>
						<i
							className={clsx(
								"hidden text-xl max-md:block cursor-pointer transition-transform duration-200 text-black",
								!isOpen.isResources ? "ri-add-line" : "ri-subtract-fill",
							)}
							onClick={() => setIsOpen((prevState) => ({ ...prevState, isResources: !prevState.isResources }))}
						></i>
					</div>

					<Collapse open={isOpen.isResources}>
						<ul className="space-y-3">
							<li><Link href="/whitepaper" className="text-black hover:text-gray-900 transition-colors">{t("menu.whitepaper")}</Link></li>
							<li><Link href="/academy" className="text-black hover:text-gray-900 transition-colors">{t("menu.academy")}</Link></li>
							<li><Link href="/appstore" className="text-black hover:text-gray-900 transition-colors">{t("menu.appstore")}</Link></li>
							<li><Link href="/university" className="text-black hover:text-gray-900 transition-colors">{t("menu.university")}</Link></li>
							<li><Link href="/events" className="text-black hover:text-gray-900 transition-colors">{t("menu.events") }</Link></li>
							<li><Link href="/help" className="text-black hover:text-gray-900 transition-colors">{t("menu.help")}</Link></li>
							<li><Link href="/legal" className="text-black hover:text-gray-900 transition-colors">{t("menu.legal")}</Link></li>
						</ul>
					</Collapse>
				</div>

				{/* 联系我们 */}
				<div>
					<div className="max-md:flex max-md:justify-between max-md:border-b max-md:border-gray-300 max-md:pb-3">
						<h3 className="mb-6 text-[14px]  text-[#4C5D66] max-md:mb-0">{t("menu.contact") || "联系我们"}</h3>
						<i
							className={clsx(
								"hidden text-xl max-md:block cursor-pointer transition-transform duration-200 text-black",
								!isOpen.isSupport ? "ri-add-line" : "ri-subtract-fill",
							)}
							onClick={() => setIsOpen((prevState) => ({ ...prevState, isSupport: !prevState.isSupport }))}
						></i>
					</div>

					<Collapse open={isOpen.isSupport}>
						<ul className="space-y-3">
							<li><Link href="/services" className="text-black hover:text-gray-900 transition-colors">{t("menu.services") || "我们的服务"}</Link></li>
						</ul>
					</Collapse>
				</div>

				{/* 扫码关注 */}
				<div className="max-lg:col-span-3 max-md:col-span-1">
					<div className="max-md:flex max-md:justify-between max-md:border-b max-md:border-gray-300 max-md:pb-3">
						<h3 className="mb-6 text-[14px]  text-[#4C5D66] max-md:mb-0">{t("menu.followUs") || "扫码关注"}</h3>
						<i
							className={clsx(
								"hidden text-xl max-md:block cursor-pointer transition-transform duration-200 text-black",
								!isOpen.isEmail ? "ri-add-line" : "ri-subtract-fill",
							)}
							onClick={() => setIsOpen((prevState) => ({ ...prevState, isEmail: !prevState.isEmail }))}
						></i>
					</div>
					<Collapse open={isOpen.isEmail}>
						<div className="flex gap-6 flex-wrap">
							{/* 微信公众号二维码 */}
							<div className="text-center">
								<div className="w-24 h-24 bg-gray-200 border border-gray-300 mb-2 flex items-center justify-center">
									<span className="text-xs text-gray-500">微信公众号</span>
								</div>
								{/* <p className="text-xs text-black">{t("footer.wechatPublic") || "关注公众号"}</p>
								<p className="text-xs text-black">{t("footer.getLatestInfo") || "获取最新资讯"}</p> */}
							</div>
							{/* 微信客服二维码 */}
							<div className="text-center">
								<div className="w-24 h-24 bg-gray-200 border border-gray-300 mb-2 flex items-center justify-center">
									<span className="text-xs text-gray-500">微信客服</span>
								</div>
								{/* <p className="text-xs text-black">{t("footer.wechatService") || "关注客服号"}</p>
								<p className="text-xs text-black">{t("footer.getSupport") || "获取专业支持"}</p> */}
							</div>
						</div>
					</Collapse>
				</div>
			</div>

			{/* 联系信息 */}
			<div className="container border-t border-b">
				<div className="grid grid-cols-2 gap-8 py-8 text-[14px] max-md:grid-cols-1 max-md:gap-4 ">
					{/* 左侧联系信息 */}
					<div>
						<h4 className="text-[14px]  text-[#4C5D66] mb-4">{t("footer.contactInfo") || "联系我们"}</h4>
						<div className="space-y-2 text-black">
							<p><span className="font-medium">{t("footer.brandCooperation") || "品牌合作"}:</span> <EMAIL></p>
							<p><span className="font-medium">{t("footer.customerSupport") || "客服支持"}:</span> <EMAIL></p>
							<p><span className="font-medium">{t("footer.chinaHQ") || "中国总部"}:</span> 广东省广州市番禺区大夫山文体创意产业园</p>
						</div>
					</div>

					{/* 右侧最新博客 */}
					<div>
						<h4 className="text-[14px]  text-[#4C5D66] mb-4">{t("footer.latestBlogs") || "最新博客"}</h4>
						<div className="space-y-2 text-black">
							<p>{t("footer.blog1") || "请添加Aiper的多渠道运营经验分享"}</p>
							<p>{t("footer.blog2") || "店匠科技携手谷歌举办跨境电商领航者峰会，分享独立站突围之道"}</p>
							<p>{t("footer.blog3") || "18个实用POD定制T恤创意灵感"}</p>
							<p>{t("footer.blog4") || "POD 定制网站费用：店匠Shoplazza与WordPress建站成本对比"}</p>
							<p>{t("footer.blog5") || "以连接推动增长：Meta Conversations 2025 首次落地中国，店匠科技受邀出席并分享"}</p>
						</div>
					</div>
				</div>
			</div>

			{/* 底部版权和语言选择 */}
			<div className="bg-white">
				<div className="container">
					<div className="flex items-center justify-between py-4 text-[12px] text-gray-500 max-md:flex-col max-md:gap-3">
						<div className="flex items-center gap-1 flex-wrap">
							<span>© 2025 广州品推科技有限公司 |</span>
							<Link href="https://beian.miit.gov.cn/" className="hover:text-gray-700 text-gray-500">粤ICP备17084964号</Link>
							<span>|</span>
							<div className="flex items-center gap-1">
								<Link href="/terms" className="hover:text-gray-700 text-gray-500">{t("footer.terms") || "服务条款"}</Link>
								<span>|</span>
								<Link href="/privacy" className="hover:text-gray-700 text-gray-500">{t("footer.privacy") || "隐私政策"}</Link>
								<span>|</span>
								<Link href="/cookies" className="hover:text-gray-700 text-gray-500">{t("footer.cookies") || "使用协议"}</Link>
							</div>
						</div>

						<div className="flex items-center gap-4">
							<Translate />
							{/* 社交媒体图标 */}
							<div className="flex items-center gap-2">
								<Follow />
							</div>
						</div>
					</div>
				</div>
			</div>
		</footer>
	);
};

export default Footer;


