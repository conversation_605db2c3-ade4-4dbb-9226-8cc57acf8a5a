'use client'
import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";

interface TwoColumnLayoutProps {
  // 布局方向
  layout?: 'left-text-right-image' | 'left-image-right-text';
  // 背景颜色
  backgroundColor?: string;
  // 文字内容（无论是左侧还是右侧）
  textContent?: {
    title?: string;
    description1?: string;
    description2?: string;
    bulletPoints?: string[];
    buttonText?: string;
    buttonOnClick?: () => void;
  };
  // 图片内容（无论是左侧还是右侧）
  imageContent?: {
    imageSrc?: string;
    imageAlt?: string;
    customComponent?: React.ReactNode;
  };
  // 容器样式
  containerClassName?: string;
  // 左侧样式
  leftClassName?: string;
  // 右侧样式
  rightClassName?: string;
}

const TwoColumnLayout: React.FC<TwoColumnLayoutProps> = ({
  layout = 'left-text-right-image',
  backgroundColor = 'bg-[#f8f4ec]',
  textContent,
  imageContent,
  containerClassName = '',
  leftClassName = '',
  rightClassName = ''
}) => {
  const [isInView, setIsInView] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.unobserve(entry.target);
        }
      },
      {
        threshold: 0.3, // 当30%的内容进入视口时触发
        rootMargin: '0px 0px -100px 0px' // 提前100px触发
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => {
      if (containerRef.current) {
        observer.unobserve(containerRef.current);
      }
    };
  }, []);

  const renderTextContent = () => (
    <div className="flex-1 flex flex-col justify-center h-full">
      <div className={`max-w-xl transition-all duration-1000 ease-out transform ${
        isInView 
          ? 'translate-x-0 opacity-100' 
          : 'translate-x-[-100px] opacity-0'
      }`}>
        {textContent?.title && (
          <h1 className="text-4xl max-md:text-2xl font-bold text-[#222222] mb-6 max-md:mb-4 leading-tight">
            {textContent.title}
          </h1>
        )}
        {textContent?.description1 && (
          <p className="text-lg max-md:text-base text-[#222222] mb-8 max-md:mb-6 leading-relaxed">
            {textContent.description1}
          </p>
        )}
        {textContent?.description2 && (
          <p className="text-lg max-md:text-base text-[#222222] mb-8 max-md:mb-6 leading-relaxed">
            {textContent.description2}
          </p>
        )}
        {textContent?.bulletPoints && textContent.bulletPoints.length > 0 && (
          <div className="mb-8 max-md:mb-6">
            {textContent.bulletPoints.map((point, index) => (
              <div key={index} className="flex items-start mb-4 max-md:mb-3">
                <div className="w-2 h-2 bg-[#222222] rounded-full mt-2 mr-3 flex-shrink-0"></div>
                <p className="text-lg max-md:text-base text-[#222222] leading-relaxed">{point}</p>
              </div>
            ))}
          </div>
        )}
        {textContent?.buttonText && (
          <button 
            className="bg-[#222222] text-white px-8 max-md:px-6 py-3 max-md:py-2 rounded-sm font-medium hover:bg-gray-700 transition-colors text-base max-md:text-sm"
            onClick={textContent.buttonOnClick}
          >
            {textContent.buttonText}
          </button>
        )}
      </div>
    </div>
  );

  const renderImageContent = () => (
    <div className="flex-1 relative">
      <div className={`transition-all duration-1000 ease-out transform delay-300 ${
        isInView 
          ? 'translate-x-0 opacity-100' 
          : 'translate-x-[100px] opacity-0'
      }`}>
        {imageContent?.imageSrc ? (
          <Image 
            src={imageContent.imageSrc} 
            alt={imageContent.imageAlt || 'image'} 
            width={800} 
            height={800}
            className="max-md:w-full max-md:h-auto"
          />
        ) : imageContent?.customComponent ? (
          imageContent.customComponent
        ) : null}
      </div>
    </div>
  );

  return (
    <div className={`${backgroundColor}`} ref={containerRef}>
      <div className={`container mx-auto flex max-w-[1200px] gap-20 max-md:flex-col max-md:gap-8 py-20 max-md:py-10 px-4 max-md:px-4 ${containerClassName}`}>
        {layout === 'left-text-right-image' ? (
          <>
            <div className={`flex-[1_1_0] ${leftClassName} max-md:order-2`}>
              {renderTextContent()}
            </div>
            <div className={`flex-[1_1_0] ${rightClassName} max-md:order-1`}>
              {renderImageContent()}
            </div>
          </>
        ) : (
          <>
            <div className={`flex-[1_1_0] ${leftClassName} max-md:order-1`}>
              {renderImageContent()}
            </div>
            <div className={`flex-[1_1_0] ${rightClassName} max-md:order-2`}>
              {renderTextContent()}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default TwoColumnLayout;