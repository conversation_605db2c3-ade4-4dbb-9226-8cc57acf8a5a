import React from "react";
import { motion } from 'framer-motion';

const VisionMission = () => {
  return (
    <section>
      <div className="md:py-[120px] py-[60px] bg-[#C7E3FF]">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 md:gap-20 gap-12 items-center">
            {/* Content */}
            <motion.div
              initial={{ opacity: 0, x: 50 }} // 从右向左（从里向外）
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="space-y-8"
            >
              <div>
                <h2 className="md:text-[40px] text-2xl font-bold text-gray-900 mb-6">
                  我们的使命
                </h2>
                <p className="text-[#222222] md:text-[16px] text-[14px] leading-relaxed">
                  我们的使命是致力于提供一个融合了线上与线下购物体验的全方位解决方案，创造一个无缝互联的购物世界。我们憧憬着将购物从单纯的交易行为转变为一场充满激情的探索旅程，并让零售更智能化。
                </p>
              </div>
            </motion.div>

            {/* Office Image */}
            <motion.div
              initial={{ opacity: 0, x: -50 }} // 从左向右（从里向外）
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <img
                src="/image/abouts/10010.jpg"
                alt="Office Team"
                className="shadow-lg w-full"
              />
            </motion.div>
          </div>
        </div>      
      </div>
    </section>
  );
};

export default VisionMission;
