// app/blog/page.tsx
import React from "react";
import { getBlogCls, getBlogList, getBlogTags } from "@/lib/api/blog";
import Breadcrumb from "@/components/Breadcrumb/Breadcrumb";
import RecentPosts from "@/components/Blogs/RecentPosts";
import Categories from "@/components/Blogs/Categories";
import TagsCloud from "@/components/Blogs/Tags";
import { type MyPageProps } from "@/lib/@types/base";
import BlogSearch from "@/components/Blogs/BlogSeach";
import type { Metadata } from "next";
import { getBasePageSeo } from "@/lib/api/seo";
import { generateSeo } from "@/lib/utils/seo";
import Blogs from "@/components/Blogs/Blogs";
import type { Blog } from "@/lib/@types/api/blog";

import BlogList from "@/components/Blogs/BlogList";
import { getTranslations, unstable_setRequestLocale } from "next-intl/server";
import { HomeTaile } from "@/components/Contact/ConcatPage";

/**
 * 生成页面的元数据
 * @param props 页面的属性，用于生成SEO信息
 * @returns 返回页面的元数据，包括SEO信息
 */
export const generateMetadata = async (props: MyPageProps): Promise<Metadata> => {
	props.params.page = ["blog"];
	// 获取基础页面的SEO数据
	const seo = await getBasePageSeo(props);
	// 生成最终的SEO信息，并返回
	return generateSeo(props, {
		...seo,
		ogType: "website",
	});
};
const fetchBlogList = async (
	Params: Blog.GetBlogListParams,
): Promise<{ blogList: Blog.BlogListItem[]; blogCount: number }> => {
	try {
		const res = await getBlogList(Params);

		return { blogList: res.detail.blog_list, blogCount: res.detail.blog_filter_count };
	} catch (error) {
		console.error("Error fetching blog list:", error);
		return { blogList: [], blogCount: 0 }; // 如果发生错误，返回一个空数组
	}
};
// 使用async函数获取数据并渲染
export default async function Blog({ params, searchParams }: MyPageProps) {
	unstable_setRequestLocale(params.locale);
	const t = await getTranslations();
	// 获取分页参数，默认为第1页
	const currentPage = 1;
	const pageSize = 6; // 每页显示6条数据

	let { blogList, blogCount } = await fetchBlogList({
		lang_code: { lang_code: params.locale },
		page: currentPage,
		limit: pageSize,
		blog_title: searchParams?.search || "",
	});

	// 获取其他数据
	const clsList = await getBlogCls({ lang_code: { lang_code: params.locale } });
	const tagList = await getBlogTags({ lang_code: { lang_code: params.locale } });

	return (
		<div className="pt-[60px] md:pt-[80px] lg:pt-[100px]">
			{/* <HomeTaile msg={t("common.Blog")} /> */}
			<section className="blog list py-6 md:py-10 lg:py-20">
				<div className="font-HarmonyOS mx-auto max-w-[1592px] px-4 sm:px-6 lg:px-8">
					{/* 响应式图片展示区域 */}
					<div className="mb-6 md:mb-10">
						{/* 移动端：单列布局 */}
						<div className="grid grid-cols-1 gap-4 md:hidden">
							<div className="cursor-pointer overflow-hidden rounded-lg">
								<img
									src="/image/resources/b1.webp"
									alt="resource-1"
									className="h-[200px] w-full rounded-lg object-cover transition-all duration-500 hover:scale-105 sm:h-[250px]"
								/>
							</div>
							<div className="grid grid-cols-2 gap-4">
								<div className="overflow-hidden rounded-lg">
									<img
										src="/image/resources/b2.webp"
										alt="resource-2"
										className="h-[120px] w-full cursor-pointer rounded-lg object-cover transition-all duration-500 hover:scale-105 sm:h-[150px]"
									/>
								</div>
								<div className="overflow-hidden rounded-lg">
									<img
										src="/image/resources/b3.webp"
										alt="resource-3"
										className="h-[120px] w-full cursor-pointer rounded-lg object-cover transition-all duration-500 hover:scale-105 sm:h-[150px]"
									/>
								</div>
							</div>
						</div>

						{/* 平板端和桌面端：原有的4列布局 */}
						<div className="hidden grid-cols-4 gap-4 md:grid">
							<div className="col-span-3 cursor-pointer overflow-hidden rounded-lg">
								<img
									src="/image/resources/b1.webp"
									alt="resource-1"
									className="h-full w-full rounded-lg object-cover transition-all duration-500 hover:scale-105"
								/>
							</div>
							<div className="col-span-1 flex flex-col gap-4">
								<div className="overflow-hidden rounded-lg">
									<img
										src="/image/resources/b2.webp"
										alt="resource-2"
										className="max-h-[360px] w-full cursor-pointer rounded-lg object-cover transition-all duration-500 hover:scale-105"
									/>
								</div>
								<div className="overflow-hidden rounded-lg">
									<img
										src="/image/resources/b3.webp"
										alt="resource-3"
										className="max-h-[360px] w-full cursor-pointer rounded-lg object-cover transition-all duration-500 hover:scale-105"
									/>
								</div>
							</div>
						</div>
					</div>

					{/* 主要内容区域 */}
					<div className="flex flex-col gap-8 lg:flex-row lg:justify-between lg:gap-12">
						<div className="lg:flex-1">
							<Blogs
								blogLists={blogList}
								locale={params.locale}
								search={searchParams?.search || ""}
								currentPage={currentPage}
								total={blogCount}
								pageSize={pageSize}
							/>
						</div>
						<div className="lg:w-1/4 lg:pl-8 xl:pl-12">
							{/* <BlogSearch></BlogSearch> */}
							<div className="space-y-6 md:space-y-8">
								<Categories clsList={clsList} />
								<TagsCloud tagList={tagList} />
								{blogList.length > 0 && <RecentPosts blogList={blogList} />}
							</div>
						</div>
					</div>
				</div>
			</section>
		</div>
	);
}
