"use client";
import { contactInfo } from "@/lib/contacts";
import clsx from "clsx";
import { useTranslations } from "next-intl";
import React from "react";
import Svg from "../Header/Menu/Svg";
import {useRouter  } from "@/navigation";
import useMenuMobile from "@/store/useMenuMobile";
import GetInstantQuote4 from "../Contact/GetInstantQuote4";

function Index({ GoToPage, handleMenuMobile }) {
	const t = useTranslations();
	let router = useRouter();
	let contact = [
		{
			name: t(`menu.Address`),
			href: ``,
			value: `${contactInfo.address}`,
		},
		{
			name: t(`menu.Phone`),
			href: `tel:${contactInfo.phone}`,
			value: `${contactInfo.phone}`,
		},

		{
			name: t(`menu.Emall`),
			href: `mailto:${contactInfo.email}`,
			value: `${contactInfo.email}`,
		},
	];

	function toInqury() {
		handleMenuMobile();
	}
	return (
		<>
			<div className="mobileIcon mb-6 flex gap-x-3">
				<button
					onClick={() => GoToPage("/compare")}
					className="flex items-center rounded bg-gray-200 px-4 py-2 hover:bg-gray-300"
				>
					<Svg isScrolled={true} /> <span className="mx-2">{t("nav.Compare12")}</span>
				</button>
				<button
					onClick={() => GoToPage("/collection")}
					className="flex items-center rounded bg-gray-200 px-4 py-2 hover:bg-gray-300"
				>
					<i className={clsx("ri-heart-3-line text-2xl ")}></i>{" "}
					<span className="mx-2">{t("nav.Wishlist12")}</span>
				</button>
			</div>
			<div className="mobileContent">
				<div className="underline" onClick={toInqury}>
					<GetInstantQuote4 text={t("nav.Need")} />
				</div>
				{contact.map((item, index) => {
					return (
						<div key={item.name} className="mb-1">
							<span className="  text-ddd">
								{item.name}
								<span className={clsx(!item.name && "hidden")}>:</span>{" "}
								<span className={clsx((index == 2 || index == 3) && "font-bold")}>{item.value}</span>
							</span>
						</div>
					);
				})}
			</div>
		</>
	);
}

export default React.memo(Index);
