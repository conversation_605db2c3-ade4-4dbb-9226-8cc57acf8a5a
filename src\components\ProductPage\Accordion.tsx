"use client";
import { useState, useEffect } from "react";
import Image from "next/image";

const data = [
	{
		id: 1,
		type: "right",
		title: "店铺销售",
		button1: "了解详情",
		button2: "立即购买",
		description: "店铺销售是Shopify的核心功能，它允许您创建和管理您的在线商店。",
		items: [
			{
				icon: (
					<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
						<path
							d="M23.5318 16.0851L14.8569 10.604C15.1379 9.93842 15.2822 9.2255 15.2817 8.50566C15.2817 5.46989 12.7512 3 9.64084 3C6.5305 3 4 5.46989 4 8.50573C4 11.1993 5.99271 13.4458 8.61448 13.9184L8.5978 23.9976C8.59748 24.2051 8.66318 24.4074 8.78583 24.5768C8.90848 24.7462 9.08202 24.8742 9.28244 24.9431C9.48286 25.012 9.70027 25.0185 9.90461 24.9616C10.109 24.9046 10.2901 24.7872 10.4231 24.6254L15.3078 18.6803L23.0768 17.9211C23.2882 17.9004 23.4879 17.8161 23.6481 17.6799C23.8084 17.5437 23.9213 17.3622 23.9712 17.1606C24.0212 16.959 24.0058 16.7472 23.9271 16.5545C23.8483 16.3618 23.7102 16.1978 23.5318 16.0851ZM6.05082 8.50573C6.05082 6.57363 7.66132 5.00169 9.64084 5.00169C11.6204 5.00169 13.2309 6.57363 13.2309 8.50573C13.2309 8.84055 13.1823 9.17022 13.0881 9.48649L10.2061 7.66555C10.0514 7.5678 9.87259 7.51254 9.68844 7.50559C9.50429 7.49864 9.32163 7.54026 9.15966 7.62607C8.9977 7.71188 8.86242 7.83871 8.76805 7.99321C8.67369 8.14772 8.62373 8.32419 8.62343 8.50406L8.61789 11.864C7.13529 11.4329 6.05082 10.0909 6.05082 8.50573Z"
							fill="currentColor"
						></path>
					</svg>
				),
				title: "一键迁移",
				description: "支持一键搬迁、批量上传、商品采集等方式快速上传商品，提高运营效率。",
				img: "/image/product/657282688d968.png",
			},
			{
				icon: (
					<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
						<path
							d="M12.4999 5H5.74993C5.33772 5 5 5.33772 5 5.74993V22.2501C5 22.6623 5.33772 23 5.74993 23H12.4999C12.9124 23 13.2501 22.6623 13.2501 22.2501V5.74993C13.2501 5.33772 12.9124 5 12.4999 5ZM22.2501 5H15.5001C15.0876 5 14.7499 5.33772 14.7499 5.74993V12.4999C14.7499 12.9124 15.0876 13.2501 15.5001 13.2501H22.2501C22.6623 13.2501 23 12.9124 23 12.4999V5.74993C23 5.33772 22.6623 5 22.2501 5ZM22.2501 14.7499H15.5001C15.0876 14.7499 14.7499 15.0876 14.7499 15.5001V22.2501C14.7499 22.6623 15.0876 23 15.5001 23H22.2501C22.6623 23 23 22.6623 23 22.2501V15.5001C23 15.0876 22.6623 14.7499 22.2501 14.7499Z"
							fill="currentColor"
						></path>
					</svg>
				),
				title: "模板建站",
				description: "多行业海量模板",
				img: "/image/product/65728171170f7.png",
			},
			{
				icon: (
					<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
						<path
							d="M9.6 20.6C8.38451 20.6 7.41101 21.5845 7.41101 22.8C7.41101 24.0155 8.38451 25 9.6 25C10.8155 25 11.8 24.0155 11.8 22.8C11.8 21.5845 10.8155 20.6 9.6 20.6ZM10.81 15.1H19.005C19.83 15.1 20.5505 14.6435 20.93 13.967L24.8625 6.828C24.9505 6.67398 25 6.49248 25 6.3C25 5.6895 24.505 5.20002 23.9 5.20002H7.6365L6.59148 3H3V5.20002H5.20002L9.1545 13.5435L7.6695 16.2385C7.49898 16.5575 7.39998 16.915 7.39998 17.3C7.39998 18.5155 8.38451 19.5 9.6 19.5H22.8V17.3H10.0675C9.9135 17.3 9.79253 17.179 9.79253 17.025C9.79253 16.9755 9.80351 16.9315 9.82553 16.893L10.81 15.1ZM20.6 20.6C19.3845 20.6 18.411 21.5845 18.411 22.8C18.411 24.0155 19.3845 25 20.6 25C21.8155 25 22.8 24.0155 22.8 22.8C22.8 21.5845 21.8155 20.6 20.6 20.6Z"
							fill="currentColor"
						></path>
					</svg>
				),
				title: "购物车",
				description: "灵活配置购物车营销工具",
				img: "/image/product/657282973f6ff.png",
			},
			{
				icon: (
					<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
						<path
							d="M9.6 20.6C8.38451 20.6 7.41101 21.5845 7.41101 22.8C7.41101 24.0155 8.38451 25 9.6 25C10.8155 25 11.8 24.0155 11.8 22.8C11.8 21.5845 10.8155 20.6 9.6 20.6ZM10.81 15.1H19.005C19.83 15.1 20.5505 14.6435 20.93 13.967L24.8625 6.828C24.9505 6.67398 25 6.49248 25 6.3C25 5.6895 24.505 5.20002 23.9 5.20002H7.6365L6.59148 3H3V5.20002H5.20002L9.1545 13.5435L7.6695 16.2385C7.49898 16.5575 7.39998 16.915 7.39998 17.3C7.39998 18.5155 8.38451 19.5 9.6 19.5H22.8V17.3H10.0675C9.9135 17.3 9.79253 17.179 9.79253 17.025C9.79253 16.9755 9.80351 16.9315 9.82553 16.893L10.81 15.1ZM20.6 20.6C19.3845 20.6 18.411 21.5845 18.411 22.8C18.411 24.0155 19.3845 25 20.6 25C21.8155 25 22.8 24.0155 22.8 22.8C22.8 21.5845 21.8155 20.6 20.6 20.6Z"
							fill="currentColor"
						></path>
					</svg>
				),
				title: "结账页",
				description: "灵活配置结账页营销工具",
				img: "/image/product/657282af5a7fa.png",
			},
		],
	},
	{
		id: 2,
		type: "left",
		title: "店铺销售",
		button1: "了解详情",
		button2: "立即购买",
		description: "店铺销售是Shopify的核心功能，它允许您创建和管理您的在线商店。",
		items: [
			{
				icon: (
					<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
						<path d="M25 16.0967C25 19.1113 23.6456 21 21.2084 21C19.0566 21 17.9153 19.8575 15.815 16.4896L14.7356 14.7528C14.4503 14.3401 14.2372 13.9538 14.0069 13.5972C13.3159 14.7132 12.3878 16.3377 12.3878 16.3377C10.0847 20.1877 8.79219 21 6.99437 21C4.49256 21 3 19.108 3 16.1825C3 11.4608 5.74244 7 9.32156 7C11.0472 7 12.5459 7.81491 14.2991 9.95519C15.5744 8.30094 16.9838 7 18.7884 7C22.195 7 25 11.1505 25 16.0967ZM12.8794 11.9462C11.4047 9.89575 10.4422 9.28821 9.29063 9.28821C7.16281 9.28821 5.37944 12.7915 5.37944 16.2222C5.37944 17.8236 6.01469 18.7217 7.08375 18.7217C8.12188 18.7217 8.76813 18.0943 10.6313 15.2943C10.6313 15.2943 11.4803 14.0033 12.8794 11.9462ZM21.26 18.7217C22.3669 18.7217 22.8722 17.8137 22.8722 16.2486C22.8722 12.1476 21.0056 8.80547 18.6372 8.80547C17.4959 8.80547 16.5369 9.66132 15.375 11.3816C15.6981 11.8373 16.0316 12.3392 16.3822 12.8807L17.6713 14.941C19.6891 18.0481 20.1978 18.7217 21.26 18.7217Z"></path>
					</svg>
				),
				title: "Meta",
				description: "一键绑定五大Meta广告资产，快速开启 Facebook营销。",
				img: "/image/product/6572907fee62d.png",
			},
			{
				icon: (
					<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
						<path d="M14.4831 4H18.088C18.4065 6.89209 20.041 8.47326 23 8.7831V12.2334C22.1502 12.3428 19.9204 12.0282 18.0796 10.7604V11.0289C18.0796 13.2167 18.0862 15.4046 18.0796 17.5924C18.0693 20.4006 16.2768 22.8184 13.5838 23.6782C12.7181 23.9577 11.8024 24.0567 10.8954 23.969C9.98837 23.8813 9.11008 23.6087 8.31681 23.1687C7.52355 22.7287 6.83283 22.131 6.28895 21.414C5.74507 20.697 5.36003 19.8765 5.15852 19.0051C4.32498 15.4489 6.87445 11.8278 10.5578 11.3482C10.9601 11.3005 11.3649 11.2764 11.7701 11.2761C12.0349 11.2725 12.3003 11.318 12.5771 11.3429V14.9747C12.3642 14.9298 12.1634 14.8665 11.9583 14.847C10.7665 14.7317 9.79003 15.129 9.1308 16.1171C8.47157 17.1052 8.46011 18.1394 9.08617 19.1328C9.71222 20.1262 10.6742 20.5904 11.8757 20.4762C13.3926 20.332 14.4825 19.1269 14.4837 17.6143C14.4873 13.1822 14.4873 8.75019 14.4837 4.31812L14.4831 4Z"></path>
					</svg>
				),
				title: "TikTok",
				description: "快速同步TikTok商品目录，扩大商品全球曝光。",
				img: "/image/product/6572904a891a9.png",
			},
			{
				icon: (
					<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
						<path d="M22.8348 12.3618H14.1832V15.8446H19.1256C18.9125 16.9703 18.2659 17.9225 17.2913 18.5604C16.4676 19.0998 15.4164 19.4188 14.1808 19.4188C11.7886 19.4188 9.7652 17.8357 9.04204 15.7085C8.86005 15.1691 8.75469 14.5922 8.75469 13.9988C8.75469 13.4055 8.86005 12.8285 9.04204 12.2891C9.76759 10.1643 11.791 8.58124 14.1832 8.58124C15.5313 8.58124 16.7406 9.03622 17.6936 9.92743L20.3277 7.34528C18.7353 5.89121 16.6592 5 14.1832 5C10.5937 5 7.48796 7.01694 5.97699 9.95791C5.3544 11.1728 5 12.5471 5 14.0012C5 15.4552 5.3544 16.8272 5.97699 18.0421C7.48796 20.9831 10.5937 23 14.1832 23C16.664 23 18.7424 22.1932 20.2606 20.8189C21.9967 19.2522 23 16.9445 23 14.2029C23 13.565 22.9425 12.9528 22.8348 12.3618Z"></path>
					</svg>
				),
				title: "Google",
				description: "谷歌GMC双重预检提升店铺过审率，降低被封号风险。",
				img: "/image/product/65728fe60522b.png",
			},
			{
				icon: (
					<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
						<path d="M6.8 23H11.3C12.2941 23 13.1 22.1941 13.1 21.2V16.7C13.1 15.7059 12.2941 14.9 11.3 14.9H6.8C5.80589 14.9 5 15.7059 5 16.7V21.2C5 22.1941 5.80589 23 6.8 23Z"></path>
						<path d="M16.7 23H21.2C22.1941 23 23 22.1941 23 21.2V16.7C23 15.7059 22.1941 14.9 21.2 14.9H16.7C15.7059 14.9 14.9 15.7059 14.9 16.7V21.2C14.9 22.1941 15.7059 23 16.7 23Z"></path>
						<path d="M11.3 13.1H6.8C5.80589 13.1 5 12.2941 5 11.3V6.8C5 5.80589 5.80589 5 6.8 5H11.3C12.2941 5 13.1 5.80589 13.1 6.8V11.3C13.1 12.2941 12.2941 13.1 11.3 13.1Z"></path>
						<path d="M16.7 13.1H21.2C22.1941 13.1 23 12.2941 23 11.3V6.8C23 5.80589 22.1941 5 21.2 5H16.7C15.7059 5 14.9 5.80589 14.9 6.8V11.3C14.9 12.2941 15.7059 13.1 16.7 13.1Z"></path>
					</svg>
				),
				title: "社媒营销",
				description: "整合Snapchat/WhatsApp/Messenger等渠道，与顾客直接互动，最大化推广成效。",
				img: "/image/product/65728f93ca8c1.png",
			},
			{
				icon: (
					<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
						<path d="M15.5012 5.45858C15.0576 5.15968 14.5349 5 14 5C13.4651 5 12.9424 5.15968 12.4988 5.45858L12.4817 5.47028L6.1898 9.92089C5.82288 10.1707 5.52264 10.5066 5.31525 10.8991C5.10787 11.2916 4.99964 11.7288 5 12.1727V20.2837C5 21.776 6.2015 23 7.7 23H20.3C21.7985 23 23 21.776 23 20.2837V12.1727C23 11.2718 22.5563 10.4267 21.8102 9.92089L15.5183 5.47028L15.5012 5.45858ZM18.824 12.4076C18.9143 12.3286 19.0195 12.2685 19.1334 12.2307C19.2474 12.193 19.3677 12.1784 19.4873 12.1878C19.607 12.1972 19.7235 12.2305 19.8301 12.2856C19.9367 12.3408 20.0312 12.4167 20.108 12.5089C20.1848 12.601 20.2424 12.7077 20.2774 12.8225C20.3125 12.9373 20.3242 13.0579 20.3119 13.1773C20.2996 13.2966 20.2635 13.4124 20.2058 13.5176C20.1482 13.6228 20.07 13.7155 19.976 13.7901L15.728 17.3299C15.2428 17.734 14.6314 17.9553 14 17.9553C13.3686 17.9553 12.7572 17.734 12.272 17.3299L8.024 13.7901C7.92999 13.7155 7.85185 13.6228 7.79417 13.5176C7.73649 13.4124 7.70045 13.2966 7.68815 13.1773C7.67585 13.0579 7.68755 12.9373 7.72256 12.8225C7.75757 12.7077 7.81518 12.601 7.89201 12.5089C7.96883 12.4167 8.06332 12.3408 8.16992 12.2856C8.27651 12.2305 8.39305 12.1972 8.51269 12.1878C8.63232 12.1784 8.75264 12.193 8.86655 12.2307C8.98047 12.2685 9.08568 12.3286 9.176 12.4076L13.424 15.9474C13.5857 16.0821 13.7895 16.1559 14 16.1559C14.2105 16.1559 14.4143 16.0821 14.576 15.9474L18.824 12.4076Z"></path>
					</svg>
				),
				title: "邮件营销",
				description: "多种EDM/SMS营销工具灵活配置，多种策略实现召回和复购。",
				img: "/image/product/65728f4b22738.png",
			},
			{
				icon: (
					<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
						<path d="M13.5562 17.2034L4.38948 11.37C4.27129 11.2946 4.17399 11.1907 4.10656 11.0677C4.03913 10.9448 4.00374 10.8069 4.00366 10.6667C4.00366 10.5266 4.03902 10.3888 4.10646 10.266C4.1739 10.1431 4.27125 10.0394 4.38948 9.96419L13.5562 4.12999C13.69 4.04508 13.8452 4 14.0037 4C14.1621 4 14.3173 4.04508 14.4512 4.12999L23.6179 9.96419C23.7361 10.0394 23.8335 10.1431 23.9009 10.266C23.9684 10.3888 24.0037 10.5266 24.0037 10.6667C24.0036 10.8069 23.9683 10.9448 23.9008 11.0677C23.8334 11.1906 23.7361 11.2946 23.6179 11.37L14.4512 17.2034C14.3145 17.29 14.1595 17.3334 14.0037 17.3334C13.8452 17.3333 13.69 17.2882 13.5562 17.2034Z"></path>
						<path d="M21.037 14.988L23.6179 16.6305C23.7361 16.7059 23.8334 16.8098 23.9008 16.9327C23.9682 17.0557 24.0036 17.1936 24.0037 17.3338C24.0037 17.4739 23.9683 17.6117 23.9009 17.7345C23.8335 17.8573 23.7361 17.9611 23.6179 18.0363L14.4512 23.8705C14.3145 23.9572 14.1587 24.0005 14.0037 24.0005C13.8452 24.0006 13.69 23.9555 13.5562 23.8705L4.38948 18.0363C4.27125 17.9611 4.1739 17.8573 4.10646 17.7345C4.03902 17.6117 4.00366 17.4739 4.00366 17.3338C4.00375 17.1936 4.03914 17.0557 4.10657 16.9328C4.174 16.8098 4.2713 16.7059 4.38948 16.6305L6.97031 14.988L12.6637 18.6105C13.0654 18.8655 13.5287 19.0005 14.0037 19.0005C14.4787 19.0005 14.942 18.8655 15.3462 18.6097L21.037 14.988Z"></path>
					</svg>
				),
				title: "联盟营销",
				description: "打通ShareAsale/Awin/CJ等多个联盟营销平台，实现品牌曝光，持续提升商品销量。",
				img: "/image/product/65728f116d1b9.png",
			},
			{
				icon: (
					<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
						<path d="M18.8744 5.15322L22.8449 9.10738C22.9189 9.18113 22.9694 9.2751 22.9898 9.3774C23.0103 9.47971 22.9998 9.58575 22.9597 9.68212C22.9196 9.77848 22.8517 9.86085 22.7647 9.9188C22.6776 9.97676 22.5752 10.0077 22.4705 10.0077H20.7311V22.4726C20.7311 22.7639 20.494 23 20.2016 23H16.7983C16.5059 23 16.2688 22.7639 16.2688 22.4726V10.0077H14.5294C14.4247 10.0077 14.3224 9.9767 14.2353 9.91874C14.1482 9.86079 14.0804 9.77843 14.0403 9.68207C14.0002 9.58572 13.9897 9.47969 14.0102 9.3774C14.0306 9.27511 14.081 9.18115 14.155 9.10738L18.1255 5.15322C18.3227 4.94895 18.6772 4.94891 18.8744 5.15322Z"></path>
						<path d="M11.1959 12.875H14.5959C14.8881 12.875 15.125 13.0993 15.125 13.376V22.499C15.125 22.7757 14.8881 23 14.5959 23H11.1959C10.9037 23 10.6668 22.7757 10.6668 22.499V13.376C10.6668 13.0993 10.9037 12.875 11.1959 12.875Z"></path>
						<path d="M5.52906 17.1682H8.92914C9.22132 17.1682 9.45819 17.3925 9.45819 17.6692V22.499C9.45819 22.7757 9.22132 23 8.92914 23H5.52906C5.23688 23 5 22.7757 5 22.499V17.6692C5 17.3925 5.23688 17.1682 5.52906 17.1682Z"></path>
					</svg>
				),
				title: "SEO优化",
				description: "站内自动化SEO功能，内容灵活管理可配置，有效提升搜索排名，获取更多免费流量。",
				img: "/image/product/65728ed31189c.png",
			},
		],
	},
	{
		id: 3,
		type: "right",
		title: "店铺销售",
		button1: "了解详情",
		button2: "立即购买",
		description: "店铺销售是Shopify的核心功能，它允许您创建和管理您的在线商店。",
		items: [
			{
				icon: (
					<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path
							d="M17.4199 14.6489C17.2901 14.5191 17.0305 14.5191 16.9007 14.6489C15.9921 15.2979 15.0834 15.6874 14.045 15.6874C13.0066 15.6874 11.9681 15.2979 11.1893 14.6489C11.0595 14.5191 10.7999 14.5191 10.6701 14.6489C9.63164 15.5576 8.07398 15.947 6.51633 15.4278H6.38652V21.269C6.38652 22.4372 7.29516 23.0862 8.33359 23.0862H19.7564C20.9246 23.0862 21.8333 22.4372 21.8333 21.269V15.4278C20.1458 16.0768 18.4584 15.6874 17.4199 14.6489ZM11.0595 20.8796C11.0595 21.0094 10.6701 21.269 10.2807 21.269C9.89125 21.269 9.50184 21.1392 9.50184 20.8796V17.6344C9.50184 17.5046 9.89125 17.245 10.2807 17.245C10.6701 17.245 11.0595 17.3748 11.0595 17.6344V20.8796ZM23.5207 9.58654L20.9246 5.82221C20.665 5.30299 20.1458 4.91357 19.4968 4.91357H8.5932C7.94418 4.91357 7.29516 5.30299 7.03555 5.82221L4.56926 9.45674V9.58654C4.43945 9.84615 4.43945 10.1058 4.43945 10.3654C4.43945 11.923 5.47789 13.3509 6.90574 13.7403C8.4634 14.1297 9.76145 13.6105 10.6701 12.4422C10.7999 12.3124 10.7999 12.3124 11.0595 12.3124C11.1893 12.3124 11.3191 12.3124 11.4489 12.4422C12.0979 13.3509 13.1364 13.7403 14.1748 13.7403C15.343 13.7403 16.2517 13.2211 16.9007 12.4422C17.0305 12.3124 17.0305 12.3124 17.2901 12.3124C17.4199 12.3124 17.5497 12.3124 17.6795 12.4422C18.4584 13.4807 19.7564 13.9999 21.0545 13.7403C22.6121 13.4807 23.9102 11.7932 23.9102 10.2356C23.6505 9.97596 23.5207 9.84615 23.5207 9.58654Z"
							fill="currentColor"
						></path>
					</svg>
				),
				title: "应用商店",
				description: "近90%应用插件免费使用，即装即卸无代码冗余，充分降本增效",
				img: "/image/product/66d04a29b6d91.png",
			},
			{
				icon: (
					<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
						<path
							d="M24 14.6667C24 15.4167 23.3305 16.0833 22.5774 16.0833H22.41C21.9079 16.0833 21.4059 16.4167 21.1548 16.9167C20.9038 17.4167 20.9874 18.0833 21.4059 18.5L21.4895 18.5833C22.0753 19.1667 22.0753 20 21.4895 20.5833L20.569 21.5C20.0669 22.0833 19.1464 22.0833 18.6444 21.5L18.5607 21.4167C18.1423 21 17.5565 20.9167 17.0544 21.0833C17.0544 21.0833 16.9707 21.0833 16.9707 21.1667C16.4686 21.4167 16.1339 21.9167 16.1339 22.4167V22.5833C16.1339 23.3333 15.5481 24 14.7113 24H13.3724C12.6192 24 11.9498 23.4167 11.9498 22.5833V22.4167C11.9498 21.8333 11.6151 21.3333 11.113 21.1667C11.113 21.1667 11.0293 21.1667 11.0293 21.0833C10.5272 20.8333 9.94142 21 9.52301 21.4167L9.43933 21.5C8.93724 22.0833 8.01674 22.0833 7.51464 21.5L6.59414 20.5833C6.09205 20 6.09205 19.1667 6.59414 18.5833L6.67782 18.5C7.09623 18.0833 7.17992 17.5 6.92887 16.9167C6.67782 16.4167 6.17573 16 5.58996 16H5.42259C4.66945 16 4 15.4167 4 14.5833V13.25C4 12.5 4.58577 11.8333 5.42259 11.8333H5.58996C6.00837 11.9167 6.51046 11.5833 6.76151 11.0833C7.01255 10.5833 6.92887 9.91667 6.51046 9.5L6.42678 9.41667C5.92468 8.91667 5.92468 8 6.42678 7.5L7.34728 6.58333C7.84937 6.08333 8.76987 6.08333 9.27197 6.58333L9.35565 6.66667C9.77406 7.08333 10.3598 7.16667 10.8619 6.91667H10.9456C11.4477 6.66667 11.7824 6.16667 11.7824 5.66667V5.41667C11.7824 4.66667 12.3682 4 13.205 4H14.5439C15.2971 4 15.9665 4.58333 15.9665 5.41667V5.58333C16.0502 6.08333 16.3849 6.58333 16.887 6.83333H16.9707C17.4728 7.08333 18.1423 6.91667 18.477 6.58333L18.6444 6.5C19.1464 5.91667 20.0669 5.91667 20.569 6.5L21.4895 7.41667C22.0753 7.91667 22.0753 8.83333 21.4895 9.41667L21.4059 9.5C20.9874 9.91667 20.9038 10.5 21.1548 11.0833C21.4059 11.5833 21.9079 11.9167 22.41 11.9167H22.5774C23.3305 11.9167 24 12.5833 24 13.3333V14.6667ZM13.9582 9.08333C12.954 9.08333 12.2008 9.91667 12.2008 10.8333C12.2008 11.8333 13.0377 12.5833 13.9582 12.5833C14.9623 12.5833 15.7155 11.75 15.7155 10.8333C15.7155 9.83333 14.9623 9.08333 13.9582 9.08333ZM17.5565 16C17.2218 15.3333 16.8033 14.75 16.1339 14.3333C15.5481 13.9167 14.795 13.6667 13.9582 13.6667C12.3682 13.6667 10.9456 14.6667 10.3598 16C10.1088 16.6667 10.6109 17.3333 11.2803 17.3333H16.636C17.3054 17.3333 17.8075 16.5833 17.5565 16Z"
							fill="currentColor"
						></path>
					</svg>
				),
				title: "员工管理",
				description: "支持灵活配置员工敏感数据权限，保障店铺数据安全。",
				img: "/image/product/657292f96cd6a.png",
			},
			{
				icon: (
					<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
						<path
							fillRule="evenodd"
							clipRule="evenodd"
							d="M7.93023 14.0001V23H7.30232C6.69172 22.9997 6.10614 22.7574 5.67395 22.3261C5.24279 21.8941 5 21.308 5 20.6977V11.3211C5 10.9236 5.10296 10.5328 5.29882 10.1868C5.49467 9.84088 5.7768 9.55154 6.11767 9.34699L12.8153 5.32844C13.1731 5.11353 13.5826 5 14 5C14.4174 5 14.8269 5.11353 15.1847 5.32844L21.8823 9.34699C22.2232 9.55154 22.5053 9.84088 22.7012 10.1868C22.8971 10.5328 23 10.9236 23 11.3211V20.6977C22.9998 21.3083 22.7574 21.8939 22.326 22.3261C21.8941 22.7572 21.308 23 20.6977 23H20.0698V14.0001C20.0698 13.6117 19.9157 13.2391 19.6403 12.9645C19.3657 12.6891 18.9931 12.535 18.6046 12.535H9.39535C9.20296 12.5349 9.01243 12.5728 8.83472 12.6465C8.65701 12.7202 8.49559 12.8283 8.35973 12.9645C8.08427 13.2391 7.93023 13.6117 7.93023 14.0001ZM15.6744 10.5827H12.3256C12.1591 10.5827 11.9993 10.5165 11.8816 10.3988C11.7638 10.281 11.6977 10.1213 11.6977 9.95479C11.6977 9.78826 11.7638 9.62856 11.8816 9.5108C11.9993 9.39305 12.1591 9.32689 12.3256 9.32689H15.6744C15.841 9.32689 16.0007 9.39305 16.1184 9.5108C16.2362 9.62856 16.3023 9.78826 16.3023 9.95479C16.3023 10.1213 16.2362 10.281 16.1184 10.3988C16.0007 10.5165 15.841 10.5827 15.6744 10.5827Z"
							fill="currentColor"
						></path>
						<path
							d="M10.141 13.9609C9.7075 13.9609 9.35611 14.3123 9.35611 14.7458V22.9869H18.5785V14.7458C18.5785 14.3123 18.2271 13.9609 17.7936 13.9609H10.141Z"
							fill="currentColor"
						></path>
					</svg>
				),
				title: "库存管理",
				description: "支持批量商品管理、多地点库存管理，监控商品动销率 ，降低库存管理压力。",
				img: "/image/product/657292c1d5764.png",
			},
			{
				icon: (
					<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
						<path
							d="M4 11.7727V20.2764C4.00053 20.7333 4.17826 21.1714 4.49421 21.4946C4.81015 21.8177 5.23852 21.9995 5.68533 22H22.3147C22.7615 21.9995 23.1898 21.8177 23.5058 21.4946C23.8217 21.1714 23.9995 20.7333 24 20.2764V11.7727H4ZM14 19.2727H7.33333C7.15652 19.2727 6.98695 19.2009 6.86193 19.073C6.7369 18.9452 6.66667 18.7717 6.66667 18.5909C6.66667 18.4101 6.7369 18.2367 6.86193 18.1088C6.98695 17.9809 7.15652 17.9091 7.33333 17.9091H14C14.1768 17.9091 14.3464 17.9809 14.4714 18.1088C14.5964 18.2367 14.6667 18.4101 14.6667 18.5909C14.6667 18.7717 14.5964 18.9452 14.4714 19.073C14.3464 19.2009 14.1768 19.2727 14 19.2727ZM24 10.4091V8.72364C23.9995 8.26667 23.8217 7.82857 23.5058 7.50544C23.1898 7.18231 22.7615 7.00054 22.3147 7H5.68533C5.23852 7.00054 4.81015 7.18231 4.49421 7.50544C4.17826 7.82857 4.00053 8.26667 4 8.72364V10.4091H24Z"
							fill="currentColor"
						></path>
					</svg>
				),
				title: "支付渠道",
				description: "品店获得ISO27001、ISO27701、PCI等多项数据安全认证，保障商家支付和交易数据安全。",
				img: "/image/product/657292885ff8d.png",
			},
			{
				icon: (
					<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
						<path
							d="M21.528 8.16667H18V4.49167L21.528 8.16667ZM18.8 17.3333C18.8 15.4917 17.368 14 15.6 14C13.832 14 12.4 15.4917 12.4 17.3333C12.4 18.5583 13.048 19.625 14 20.2V24L15.6 22.3333L17.2 24V20.2C18.152 19.625 18.8 18.5583 18.8 17.3333ZM16.4 9.83333V4H8.4C7.072 4 6 5.11667 6 6.5V22C6 23.1046 6.89543 24 8 24H12.4V21.05C11.424 20.1333 10.8 18.8083 10.8 17.3333C10.8 14.575 12.952 12.3333 15.6 12.3333C18.248 12.3333 20.4 14.575 20.4 17.3333C20.4 18.8083 19.776 20.1333 18.8 21.05V24H20C21.1046 24 22 23.1046 22 22V9.83333H16.4Z"
							fill="currentColor"
						></path>
					</svg>
				),
				title: "订单履约",
				description: "全流程订单管理及数据同步，携手50+全球物流服务商，保障高效履约。",
				img: "/image/product/6572924d8b46d.png",
			},
			{
				icon: (
					<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
						<path
							d="M4 11.7727V20.2764C4.00053 20.7333 4.17826 21.1714 4.49421 21.4946C4.81015 21.8177 5.23852 21.9995 5.68533 22H22.3147C22.7615 21.9995 23.1898 21.8177 23.5058 21.4946C23.8217 21.1714 23.9995 20.7333 24 20.2764V11.7727H4ZM14 19.2727H7.33333C7.15652 19.2727 6.98695 19.2009 6.86193 19.073C6.7369 18.9452 6.66667 18.7717 6.66667 18.5909C6.66667 18.4101 6.7369 18.2367 6.86193 18.1088C6.98695 17.9809 7.15652 17.9091 7.33333 17.9091H14C14.1768 17.9091 14.3464 17.9809 14.4714 18.1088C14.5964 18.2367 14.6667 18.4101 14.6667 18.5909C14.6667 18.7717 14.5964 18.9452 14.4714 19.073C14.3464 19.2009 14.1768 19.2727 14 19.2727ZM24 10.4091V8.72364C23.9995 8.26667 23.8217 7.82857 23.5058 7.50544C23.1898 7.18231 22.7615 7.00054 22.3147 7H5.68533C5.23852 7.00054 4.81015 7.18231 4.49421 7.50544C4.17826 7.82857 4.00053 8.26667 4 8.72364V10.4091H24Z"
							fill="currentColor"
						></path>
					</svg>
				),
				title: "自有支付",
				description: "一键开启Shoplazza Payments，即享全球收款服务",
				img: "/image/product/6805ec339d1f2.png",
			},
		],
	},
];

const AccordionItem = ({ item }: { item: (typeof data)[0] }) => {
	const [openIndex, setOpenIndex] = useState(0);
	const [isTransitioning, setIsTransitioning] = useState(false);

	const handleItemChange = (index: number) => {
		if (index !== openIndex && !isTransitioning) {
			setIsTransitioning(true);
			setOpenIndex(index);
		}
	};

	const handleMouseEnter = (index: number) => {
		if (index !== openIndex && !isTransitioning) {
			setIsTransitioning(true);
			setOpenIndex(index);
		}
	};

	// 过渡完成后重置状态
	useEffect(() => {
		if (isTransitioning) {
			const timer = setTimeout(() => {
				setIsTransitioning(false);
			}, 300); // 与CSS过渡时间匹配
			return () => clearTimeout(timer);
		}
	}, [openIndex, isTransitioning]);

	return (
		<article>
			<div className="container mx-auto px-4">
				<div
					className={`flex flex-col items-center gap-8 lg:flex-row lg:gap-0 ${
						item.type === "right" ? "lg:flex-row-reverse" : ""
					}`}
				>
					{/* 图片区域 - 占50% */}
					<div className="w-full lg:w-[50%]">
						<div className="group relative">
							<div className="overflow-hidden shadow-lg">
								<Image
									src={item.items[openIndex].img}
									alt={item.items[openIndex].title}
									width={600}
									height={400}
									className="h-auto w-full object-cover"
									priority
								/>
							</div>
						</div>
					</div>

					{/* 中间空隙 - 占10% */}
					<div className="hidden lg:block lg:w-[10%]"></div>

					{/* 内容区域 - 占40% */}
					<div className="w-full space-y-8 lg:w-[40%]">
						{/* 标题和描述 */}
						<header className="space-y-4">
							<h2 className="text-4xl font-bold text-gray-900 lg:text-5xl">{item.title}</h2>
							<p className="text-lg text-gray-600">{item.description}</p>
						</header>
						<div className="flex gap-4">
							<button className="h-[46px] rounded-[4px] border bg-[#000] px-[24px] text-white">
								{item.button1}
							</button>
							<button className="h-[46px] rounded-[4px] border border-[#000] bg-transparent px-[24px]">
								{item.button2}
							</button>
						</div>
						{/* 功能列表 */}
						<nav role="tablist">
							{item.items.map((child, index) => (
								<div
									key={child.title}
									className={`overflow-hidden border-b bg-white ${
										openIndex === index ? "border-black" : "border-gray-200"
									}`}
								>
									<div className="py-6">
										<button
											onClick={() => handleItemChange(index)}
											onMouseEnter={() => handleMouseEnter(index)}
											className={`w-full text-left `}
											role="tab"
											aria-selected={openIndex === index}
											aria-controls={`panel-${index}`}
										>
											<div className="flex items-start gap-4">
												<div className="flex-shrink-0">{child.icon}</div>
												<div className="min-w-0 flex-1">
													<h3 className={`mb-2 text-xl transition-colors `}>{child.title}</h3>
												</div>
												{/* 展开指示器 */}
												{openIndex === index && (
													<div className="mt-1 flex-shrink-0">
														<svg
															width="20"
															height="20"
															viewBox="0 0 20 20"
															fill="none"
															className="text-gray-400"
														>
															<path
																d="M7.5 5L12.5 10L7.5 15"
																stroke="currentColor"
																strokeWidth="2"
																strokeLinecap="round"
																strokeLinejoin="round"
															/>
														</svg>
													</div>
												)}
											</div>
										</button>
										{/* 手风琴内容区域 */}
										<div
											className={`overflow-hidden transition-all duration-300 ease-in-out ${
												openIndex === index ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
											}`}
											role="tabpanel"
											aria-labelledby={`tab-${index}`}
										>
											<div>
												<div className="ml-12 space-y-4">
													<p className="text-base leading-relaxed text-gray-600">{child.description}</p>
												</div>
											</div>
										</div>
									</div>
								</div>
							))}
						</nav>
					</div>
				</div>
			</div>
		</article>
	);
};

const Accordion = () => {
	return (
		<section className="flex flex-col gap-[120px] bg-white py-[120px]">
			{data.map((item) => (
				<AccordionItem key={item.id} item={item} />
			))}
		</section>
	);
};

export default Accordion;
