"use client";
import { fetchSearchProductsData } from "@/lib/api/product";
import { useLoveStore } from "@/lib/store/love.store";
import clsx from "clsx";
import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { RingLoader } from "react-spinners";
import ProductCard from "../Product/product-card";
import EmptyState from "../EmptyState";
import { ProductListItemFragment } from "@/gql/graphql";
import Masonry from "react-masonry-css";
import { HomeTaile } from "../Contact/ConcatPage";
import CompareList from "../CompareList";
import MyEmpty from "../MyEmpty";
import { useCompareStore } from "@/lib/store/Compare.store";
import Svg from "../Header/Menu/Svg";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import { defaultLocale } from "@/config";
import { useProductStore } from "@/lib/store/product.store";
import Lightbox from "react-image-lightbox";
import "react-image-lightbox/style.css"; // 需要引入样式
import { message } from "antd";
import { useUserAuth } from "@/lib/hooks/useUserAuth";
import { useShoppingCart } from "@/lib/hooks/useShoppingCart";
import {useRouter  } from "@/navigation";
export const fetchData = async (
	ids: number[],
	setLoading: (loading: boolean) => void,
	locale: string,
): Promise<any> => {
	try {
		setLoading(true);
		const { products } = await fetchSearchProductsData({ ids: ids, locale, channel: "default-channel" });
		console.log(products.edges, "productsproductsproducts");

		return products.edges;
	} finally {
		setLoading(false);
	}
};

function Index() {
	const [compareList, setcompareList] = useState([]);
	const t = useTranslations();
  const { compareIds,removeAllCompare,removeCompareProduct } = useCompareStore();
	const [loading, setLoading] = useState(false);
	const locale = useLocale();
	useEffect(() => {
		if (!compareIds.length || !locale) return setcompareList([]);
		fetchData(compareIds, setLoading, locale).then((res) => {

        // 按照 compareIds 顺序排序
  const sortedData = compareIds
  .map((id) => res.find((item) => item.node.id === id))
  .filter(Boolean); // 去掉可能未匹配到的数据

// 更新排序后的结果
setcompareList(sortedData);
		});
	}, [compareIds, locale]);
  let router=useRouter()
  const swiperBaseConfig = {
    slidesPerView: 2,
    breakpoints: {
      320: {
        slidesPerView: 1,
      },
      768: {
        slidesPerView: 1,
      },
      1024: {
        slidesPerView: 2,
      },
    },
  };

	// console.log(compareList, "compareList");

  function DelCompareProduct(id:any){
    removeCompareProduct(id)
  }

	return (
		<div>
			<HomeTaile msg={t("nav.Compare")} />
			<div className="container my-16">
				{!loading && !!compareList.length && (
					<div className="e-flex mb-4 ">
						<div
							className="cursor-pointer text-sm duration-300 hover:text-main hover:underline"
							onClick={removeAllCompare}
						>
							<i className="ri-delete-bin-6-line"></i>
							{t("common.2404e3fb11a4cd4619b87d75ba56e345d8b0")}
						</div>
					</div>
				)}
				{loading ? (
					<div className="c-flex min-h-[70vh]">
						<RingLoader color="#000" />
					</div>
				) : compareList.length ? (
					<Swiper
						className="grid grid-cols-2  border-[1px] border-[#ebebeb] "
						modules={[]}
						{...swiperBaseConfig}
						spaceBetween={0}
						onSlideChange={() => console.log("slide change")}
						onSwiper={(swiper) => console.log(swiper)}
					>
    {compareList.map((item, index) => (
      <SwiperSlide key={index}>
        <CompareProductInfo
          key={item.databaseId}
          productItem={item.node as ProductListItemFragment}
          DelCompareProduct={DelCompareProduct}
        />
      </SwiperSlide>
    ))}
{compareList.length == 1 && (
  <SwiperSlide>
    <div className="group relative flex h-full w-full items-center justify-center border-l border-[#ebebeb]">
      {/* 背景动画效果 */}
      <div className="absolute inset-0 bg-gradient-to-b from-gray-50 to-white opacity-70" />
      <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(68,68,68,.02)_25%,rgba(68,68,68,.02)_50%,transparent_50%,transparent_75%,rgba(68,68,68,.02)_75%)]" />
      
      {/* 内容区域 */}
      <div className="relative z-10 flex flex-col items-center justify-center px-4 transform transition-all duration-500 group-hover:scale-105">
        {/* 图标区域 */}
        <div onClick={()=>router.push(`/products`)} className="relative mb-8 cursor-pointer">
          <div className="absolute -inset-1 animate-pulse rounded-full bg-gradient-to-r from-blue-100 via-purple-100 to-pink-100 opacity-75 blur-lg transition-all duration-500 group-hover:opacity-100" />
          <div className="relative flex h-24 w-24 items-center justify-center rounded-full bg-white shadow-xl">
            <i className="ri-add-line text-4xl text-gray-400 transition-all duration-300 group-hover:text-gray-600"></i>
          </div>
        </div>
        
        {/* 文字区域 */}
        <div className="space-y-4 text-center">
          <h3 className="text-2xl font-medium text-gray-700 transition-all duration-300 group-hover:text-gray-900">
            {t("nav.Add more products to compare")}
          </h3>
          <p className="max-w-xs text-sm font-light text-gray-500 transition-all duration-300 group-hover:text-gray-600">
            {t("nav.Select another product for comparison")}
          </p>
        </div>
      </div>

      {/* 装饰元素 */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-gray-50 to-transparent opacity-50" />
      <div className="absolute -right-4 -top-4 h-32 w-32 rounded-full bg-gradient-to-br from-purple-50 to-pink-50 opacity-20 blur-xl" />
      <div className="absolute -bottom-4 -left-4 h-32 w-32 rounded-full bg-gradient-to-tr from-blue-50 to-green-50 opacity-20 blur-xl" />
    </div>
  </SwiperSlide>
)}


					</Swiper>
				) : (
					<MyEmpty
						text={t("nav.Compare list")}
						description={t("nav.No products")}
						className="py-20 max-md:py-4"
					>
						<Svg isScrolled={true} />
					</MyEmpty>
				)}
			</div>
		</div>
	);
} 

export default React.memo(Index);

function CompareProductInfo({ productItem,DelCompareProduct }: { productItem: any,DelCompareProduct:(id:string)=>void }) {
	// console.log(productItem, "productItem");
	const pt = productItem.media;
	const imgs =  JSON.parse(pt)|| [];
	let locale = useLocale();
	let { currencyUnit } = useProductStore();

	const t = useTranslations('nav');
	let isvariants = productItem.variants?.some((item) => item.quantityAvailable > 0)||false;
	let { variants } = productItem;

	const attributes = productItem?.attributes?.map((item) => {
		return {
			label: item.attribute?.translation?.name || item.attribute.name,
			value: item.values.map((i) => i.translation?.name || i.name).join(", "),
		};
	});
  let router=useRouter()
    const [isOpen, setIsOpen] = useState(false);
    const [imageUrl, setimageUrl] = useState(imgs[0]?.url || "/image/default-image.webp"); // 替换为你的图片 URL
    const [loading, setLoading] = useState(false);
  const { isLogin } = useUserAuth();
    const { addToCart } = useShoppingCart();
    	// 添加购物车
	async function AddCart() {
		if (!isLogin()){
      const login = document.querySelector("#web-login") as HTMLLIElement;
      return  login.click();
    }  

		setLoading(true);
		// @ts-ignore
		await addToCart({
			channel: "default-channel",
			selectedVariantID: productItem.variants[0].id,
			quantity: 1,
		});
		return setLoading(false);
	}
	



	return (
		<div className=" w-full  border-l-[1px] border-r-[1px] border-[#ebebeb]">
			<div className="py-4 text-center">
				<div onClick={()=>DelCompareProduct(productItem.id)} className="cursor-pointer text-sm text-black hover:underline">{t('Remove')}</div>
			</div>
			<div className="mb-6 rounded-lg ">
				<div className="mb-6 flex justify-center pb-10">
					<img
						alt="Black elongated pickleball paddle with SST CORE branding and a stylized S logo in gold, featuring a sleek design with curved lines and PRO text on the face"
						className="h-[850px] w-full object-cover"
						height="600"
						src={imageUrl}
						width="400"
					/>
				</div>
				<div className="text-center">
					<h2 className="mb-2 text-lg font-medium line-clamp-1">
						{locale === defaultLocale
							? productItem.name
							: productItem.translation
								? productItem.translation.name
								: productItem.name}
					</h2>
					<p className="mb-6 text-lg font-bold">
						{currencyUnit} {renderPrice(productItem.pricing?.priceRange) || null}
					</p>
					<div className="flex justify-center gap-4">
						<button onClick={()=>setIsOpen(true)} className="flex items-center rounded bg-gray-200 px-4 py-2 hover:bg-gray-300">
							<i className="far fa-eye mr-2"></i>
							{t('QUICK VIEW')}
						</button>
            {
              process.env.NEXT_PUBLIC_SITE_TYPE == "toc"&&productItem.variants.length>0&&<button onClick={()=>AddCart()} className="flex items-center rounded bg-gray-200 px-4 py-2 hover:bg-gray-300">
							<i className="fas fa-shopping-cart mr-2"></i>
							
              {loading ? (
      <div className="flex items-center justify-center gap-2 ">
        <GetSVG />
        
        <span>{t('loading')}</span>
      </div>
    ) : (
      <span>{t('ADD TO CART')}</span>
    )}
						</button>
            }

{
            <button onClick={()=>router.push(`/product/${productItem.slug}`)} className="flex items-center rounded bg-gray-200 px-4 py-2 hover:bg-gray-300">
							<i className="fas fa-shopping-cart mr-2"></i>
							
              <span>{t('PRODUCT DETAILS')}</span>
						</button>
            }
					</div>
				</div>
			</div>
			<div className="border-t border-gray-200">
				<div className="flex  bg-gray-100 px-4 py-4">
					<div className="block pr-6 text-gray-700 ">{t('Availability')} :</div>
					<div className="flex flex-1 items-center text-green-600 ">
						<i className="fas fa-check-circle mr-2"></i>
						{isvariants ? (
							<span>{t("IN STOCK")}</span>
						) : (
							<span className="text-[#d53a3d]">{t("OUT OF STOCK")}</span>
						)}
					</div>
				</div>
				{/* variants */}
				<div className="flex  bg-white px-4 py-4">
					<div className="block pr-6 text-gray-700">{t("Variants")} :</div>
					<div className="flex flex-1 items-center text-[#545454] text-sm line-clamp-1">
						{variants&&variants?.map((item, index) => (
							<span key={item.id}>
								{item?.translation?.name || item?.name}
								{index < variants.length - 1 && ","}
							</span>
						))}
					</div>
				</div>

        {/* 属性 */}
				{attributes &&
					attributes?.map((item, i) => {
						return (
							<div key={i} className={clsx("flex   px-4 py-4",i%2==0?'bg-gray-100':'bg-white')}>
								<div className="block pr-6 text-gray-700">{item.label} :</div>
								<div className="flex flex-1 items-center text-[#545454] text-sm line-clamp-1">{item.value}</div>
							</div>
						);
					})}
			</div>

            {isOpen && <Lightbox mainSrc={imageUrl} onCloseRequest={() => setIsOpen(false)} />}
		</div>
	);
}

const renderPrice = (price) => {
	if (!price?.start || !price?.stop) return null;
	return price.stop.gross.amount;
};


function GetSVG(){
  return (<svg 
    className="animate-spin h-4 w-4 text-white" 
    xmlns="http://www.w3.org/2000/svg" 
    fill="none" 
    viewBox="0 0 24 24"
  >
    <circle 
      className="opacity-25" 
      cx="12" 
      cy="12" 
      r="10" 
      stroke="currentColor" 
      strokeWidth="4"
    />
    <path 
      className="opacity-75" 
      fill="currentColor" 
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    />
  </svg>)
}