"use client";
import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { Popover } from "antd";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { useHeaderHeight } from "@/lib/hooks/useHeaderHeight";
import LoginModal from "@/components/User/login-modal";
import { CountUpNumber } from "@/components/CountUpNumber/page";
import { useTranslations } from "next-intl";
import { Link } from "@/navigation";
// 咨询弹出层组件
export const ConsultationPopover = ({ theme = "black" }: { theme?: "white" | "black" }) => {
	const content = (
		<div className="p-3">
			<div className="text-center">
				<div className="mb-2">
					{/* 二维码占位图 */}
					<div className="mx-auto flex h-[120px] w-[120px] flex-col items-center justify-center rounded border border-gray-200 bg-gray-50">
						<div className="mb-1 flex h-16 w-16 items-center justify-center rounded bg-gray-300">
							<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#666666" strokeWidth="2">
								<rect x="3" y="3" width="7" height="7" />
								<rect x="14" y="3" width="7" height="7" />
								<rect x="3" y="14" width="7" height="7" />
								<path d="m5 5 2 2" />
								<path d="m5 17 2 2" />
								<path d="m17 5 2 2" />
								<path d="m15 15 1 1" />
								<path d="m18 18 1 1" />
							</svg>
						</div>
						{/* <span className="text-xs text-gray-500">微信二维码</span> */}
					</div>
				</div>
				<p className="text-[12px] text-titleLight">扫码添加客户经理，获取1V1开店咨询</p>
			</div>
		</div>
	);

	return (
		<Popover content={content} trigger="hover" placement="bottom" overlayClassName="consultation-popover">
			<Link href="/contact-us"
				className={`rounded ${theme === "white" ? "border-white text-white hover:text-white" : "border-black text-titleLight block hover:text-titleLight"
					} border-[1px] px-[24px] py-[11px] text-[16px] font-medium   transition-colors`}
			>
				联系我们
			</Link>
		</Popover>
	);
};

export default function BannerStatistics() {
	const paddingTop = useHeaderHeight();
	const [paddingLeft, setPaddingLeft] = useState(0);
	// 登录模态框状态管理
	const [openLoginModal, setOpenLoginModal] = useState(false);
	const [activeId, setActiveId] = useState(1); // 1: 登录, 2: 注册

	// 处理注册按钮点击
	const handleRegisterClick = () => {
		setActiveId(2); // 设置为注册面板
		setOpenLoginModal(true);
	};

	useEffect(() => {
		const calculatePadding = () => {
			const windowWidth = window.innerWidth;
			const maxWidth = 1577;
			const containerWidth = Math.min(windowWidth, maxWidth);
			const sideMargin = (windowWidth - containerWidth) / 2;

			const rem = parseFloat(getComputedStyle(document.documentElement).fontSize);
			// The container has `px-4`, which is `1rem`.
			const containerPadding = rem * 1;

			setPaddingLeft(sideMargin + containerPadding);
		};

		calculatePadding();
		window.addEventListener("resize", calculatePadding);

		return () => {
			window.removeEventListener("resize", calculatePadding);
		};
	}, []);
	const containerVariants = {
		hidden: { opacity: 0 },
		visible: {
			opacity: 1,
			transition: {
				duration: 0.8,
				staggerChildren: 0.15,
				delayChildren: 0.2,
			},
		},
	};

	const leftContentVariants = {
		hidden: {
			opacity: 0,
			x: -30,
			y: 20,
		},
		visible: {
			opacity: 1,
			x: 0,
			y: 0,
			transition: {
				duration: 0.6,
				ease: "easeOut",
				staggerChildren: 0.1,
			},
		},
	};

	const rightContentVariants = {
		hidden: {
			opacity: 0,
			x: 30,
			y: 20,
		},
		visible: {
			opacity: 1,
			x: 0,
			y: 0,
			transition: {
				duration: 0.6,
				ease: "easeOut",
				delay: 0.2,
			},
		},
	};

	const textItemVariants = {
		hidden: {
			opacity: 0,
			y: 20,
		},
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				duration: 0.5,
				ease: "easeOut",
			},
		},
	};

	const statisticsVariants = {
		hidden: {
			opacity: 0,
			y: 15,
		},
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				duration: 0.4,
				ease: "easeOut",
			},
		},
	};

	const bannerVariants = {
		hidden: {
			opacity: 0,
		},
		visible: {
			opacity: 1,
			transition: {
				duration: 0.6,
				ease: "easeOut",
				delay: 0.3,
			},
		},
	};

	const buttonVariants = {
		hidden: {
			opacity: 0,
			y: 15,
		},
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				duration: 0.4,
				ease: "easeOut",
			},
		},
	};

	const statisticsData = [
		{ number: "500", suffix: "+", label: "合作伙伴", color: "text-red-500" },
		{ number: "15", suffix: "亿+", label: "销售消费者", color: "text-red-500" },
		{ number: "50", suffix: "万+", label: "在线商家", color: "text-red-500" },
		{ number: "180", suffix: "+", label: "覆盖国家地区", color: "text-red-500" },
	];

	const bannerData = [
		{
			title: "免费建站（PinShop 基础版实操指南）",
			description: "从0到1建站教程，TikTok广告投放实操指南",
		},
		{
			title: "免费客服（Pinshop基础版客服插件）",
			description: "AI智能客服系统，7*24小时在线，多语言支持",
		},
		{
			title: "独立站全球支付方案",
			description: "集成PayPal，轻松实现全球收款，助力跨境电商业务发展",
		},
		{
			title: "合作伙伴计划",
			description: "合作伙伴优惠，我们为您提供建站、运营、营销一站式服务",
		},
		// 放个空的站位避免超出屏幕外
		{
			title: "",
			description: "",
		},
	];

	return (
		<>
			<div className="relative bg-mainPrimaryLight" style={{ paddingTop }}>
				{/* Hero Section */}
				<div className="container py-16 max-md:py-8">
					<div className="grid items-center gap-8  lg:gap-16 xl:grid-cols-5">
						{/* Left Content */}
						<motion.div
							className="space-y-6 xl:col-span-2 xl:space-y-8"
							initial="hidden"
							whileInView="visible"
							viewport={{ once: true, amount: 0.3 }}
							variants={leftContentVariants}
						>
							<div className="space-y-4">
								<motion.p className="text-sm font-medium text-titleLight" variants={textItemVariants}>
									5分钟快速建站，开启跨境电商新时代
								</motion.p>
								<motion.h1
									className="text-3xl font-bold leading-tight text-titleLight md:text-4xl lg:text-[40px]"
									variants={textItemVariants}
								>
									用 Pinshop 快速打造更智能的网站
								</motion.h1>
								<motion.p
									className="text-base leading-relaxed text-titleLight lg:text-lg"
									variants={textItemVariants}
								>
									Pinshop 提供基于 React 与 Vercel 架构的高性能建站服务，集成 AI SEO、视频生成与数字人客服等智能内容能力，助力企业快速上线与持续增长。
								</motion.p>
							</div>

							<motion.div className="flex  flex-row gap-4" variants={buttonVariants}>
								<Link
									// onClick={handleRegisterClick}
									href="/pricing"
									className={`rounded block bg-black px-[24px] py-[11px] text-[16px] font-medium text-white hover:bg-opacity-80 hover:text-white`}
								>
									立即建站
								</Link>
								<motion.div>
									<ConsultationPopover />
								</motion.div>
							</motion.div>
						</motion.div>

						{/* Right Content - Fashion Image */}
						<motion.div
							className="relative xl:col-span-3"
							initial="hidden"
							whileInView="visible"
							viewport={{ once: true, amount: 0.3 }}
							variants={rightContentVariants}
						>
							<SEOOptimizedImage
								src="https://www.shoplazza.cn/upload/adspic/2023-12/657122fc350f7.png"
								priority
								alt="Pinshop Saleor Banner Statistics"
								width={1000}
								height={1000}
								className="h-auto w-full object-contain"
							/>
						</motion.div>
					</div>
				</div>

				{/* Orange Banner Section - Desktop */}
				<motion.div
					className="absolute -bottom-[70px] left-0 w-full rounded-lg bg-[#ffa180]  py-6 max-xl:hidden"
					style={{
						marginLeft: paddingLeft,
						willChange: "opacity",
						backfaceVisibility: "hidden",
						transform: "translateZ(0)",
					}}
					initial="hidden"
					whileInView="visible"
					viewport={{ once: true, amount: 0.3 }}
					variants={bannerVariants}
				>
					<div className="px-6">
						<div className="flex">
							{bannerData.map((item, index) => (
								<motion.div
									key={index}
									className="group  relative flex-1 cursor-pointer border-r border-white/20 px-6 py-4 transition-all duration-300 first:pl-0 last:pr-0"
								// variants={textItemVariants}
								>
									<div className="mb-1 flex items-center justify-between">
										<h3 className="text-[16px] leading-tight text-titleLight underline decoration-transparent decoration-1 underline-offset-2 transition-all duration-300 group-hover:decoration-titleLight">
											{item.title}
										</h3>
										<svg
											className="ml-2 h-4 w-4 flex-shrink-0 text-titleLight/60 transition-transform duration-300 group-hover:translate-x-1"
											fill="none"
											stroke="currentColor"
											viewBox="0 0 24 24"
										>
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
										</svg>
									</div>
									<p className="mt-4 text-xs leading-relaxed text-titleLight/80">{item.description}</p>
								</motion.div>
							))}
						</div>
					</div>
				</motion.div>
			</div>
			{/* Statistics Section */}
			<motion.div
				className="bg-white py-16 max-xl:bg-mainPrimaryMedium max-md:py-8 xl:mt-20"
				initial="hidden"
				whileInView="visible"
				viewport={{ once: true, amount: 0.3 }}
				variants={containerVariants}
			>
				<div className="container mx-auto">
					<div className="grid grid-cols-2 gap-8 lg:grid-cols-4">
						{statisticsData.map((stat, index) => (
							<motion.div
								key={index}
								className="text-center"
								variants={statisticsVariants}
								transition={{ type: "spring", stiffness: 300 }}
							>
								<div className={`text-3xl font-bold lg:text-4xl ${stat.color} mb-2`}>
									<CountUpNumber
										end={stat.number}
										className={`text-3xl font-bold max-xl:text-black lg:text-[40px] ${stat.color}`}
									/>
									<span className={`text-3xl font-bold max-xl:text-black lg:text-[40px] ${stat.color}`}>
										{stat.suffix}
									</span>
								</div>
								<div className="text-[20px]  font-medium text-titleLight max-xl:text-black">{stat.label}</div>
							</motion.div>
						))}
					</div>
				</div>
			</motion.div>

			{/* 登录模态框 */}
			<LoginModal
				openModal={openLoginModal}
				setOpenModal={setOpenLoginModal}
				activeId={activeId}
				setActiveId={setActiveId}
			/>
		</>
	);
}
