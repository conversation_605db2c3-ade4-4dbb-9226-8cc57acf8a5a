"use client";
import ProductCard from "@/components/Product/product-card";
import { ProductListItemFragment, ProductListPaginatedQuery } from "@/gql/graphql";
import { Empty, message, Spin } from "antd";
import React, { useState, useCallback } from "react";
import { fetchProductByCategoryData, fetchProductData } from "@/lib/api/product";
import { Button } from "../Button";
import { useTranslations } from "next-intl";

import Masonry from "react-masonry-css";
import CompareList from "../CompareList";
import { useCompareStore } from "@/lib/store/Compare.store";
import MyEmpty from "../MyEmpty";
import { useBreakpointColumns } from "@/lib/store/breakpointColumnsObj";
const ProductList = ({
	products,
	locale,
	channel,
	slug,
  
}: {
	products: any;
	locale: string;
	channel: string;
	slug?: string;
}) => {
	// 搜索页面无loadmore ==> ?
	const [hasMore, setHasMore] = useState(products?.pageInfo?.hasNextPage);
	const [moreLoading, setMoreLoading] = useState(false);
	const [after, setAfter] = useState(products?.pageInfo?.endCursor);
	const [productsList, setProductsList] = useState(products);
	const t = useTranslations();

	const onLoadMore = useCallback(async () => {
		try {
			setMoreLoading(true);
			let res = null;
			if (slug) {
				res = (await fetchProductByCategoryData({ slug, locale, channel, after }))?.category;
			} else {
				res = await fetchProductData({ locale, channel, after });
			}
			setProductsList((prev) => ({
				...prev,
				edges: [...prev.edges, ...res.products.edges],
			}));
			setHasMore(res.products.pageInfo.hasNextPage);
			setAfter(res.products.pageInfo.endCursor);
		} catch (e) {
			message.error(e.message);
		} finally {
			setMoreLoading(false);
		}
	}, [locale, channel, after]);
let {breakpointColumns} = useBreakpointColumns()

	return (
		<>
			{!productsList?.edges.length && <MyEmpty text={''} description={t('nav.Nogoods')}  className="py-20 max-md:py-4">
                  <i className="ri-shopping-cart-2-fill text-4xl  !text-ddd"></i>
                </MyEmpty>}
                {/* @ts-ignore */}
      <Masonry breakpointCols={breakpointColumns}
      className="my-masonry-grid"
      columnClassName="my-masonry-grid_column">
  {productsList?.edges.map((item) => (
    <ProductCard
      key={item.node.slug}
      productItem={item.node as ProductListItemFragment}
      locale={locale}
    />
  ))}
</Masonry>
			{moreLoading ? (
				<Spin />
			) : (
				hasMore && (
					<div className="mt-6 flex justify-center">
						<Button onClick={onLoadMore} className="!rounded-sm !bg-black">
							{t("common.loadmore")}
						</Button>
					</div>
				)
			)}

      
<div className="max-md:hidden">
    <CompareList />
      </div>
		</>
	);
};

export default ProductList;
