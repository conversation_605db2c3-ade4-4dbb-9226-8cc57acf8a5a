import { executeGraphQL } from "@/lib/graphql";
import { useTranslations } from "next-intl";
import { ProductListByCategoryNewDocument } from "@/gql/graphql";
import { defaultLocale, handleGraphqlLocale } from "@/lib/utils/util";
import ProducrCard from "@/components/Product/product-card";
import { Empty, Spin } from "antd";
import CompareList from "../CompareList";
import { getTranslations } from "next-intl/server";

const WhoViewed = async ({ locale, channel, slug }: { slug: string; locale: string; channel: string }) => {
	const t = await getTranslations();
	const { category } = await executeGraphQL(ProductListByCategoryNewDocument, {
		withAuth: false,
		variables: {
			locale: handleGraphqlLocale(locale || defaultLocale),
			channel: channel,
			slug: slug,
		},
		revalidate: 60 * 5,
	});

	return (
		<div className="containernone border-b border-[#cad8df] pb-20">
			<h2 className="mb-10 text-2xl font-bold leading-8 max-md:mt-10">
				{t("base.people-who-viewed-this-also-viewed")}
			</h2>

			<div className="grid grid-cols-4 gap-x-4 max-md:grid-cols-2">
				{category?.products?.edges.length ? (
					category?.products?.edges.map(
						(item, index) => index < 4 && <ProducrCard key={index} productItem={item.node} locale={locale} />,
					)
				) : (
					<Empty />
				)}
			</div>

			<div className="max-md:hidden">
				<CompareList />
			</div>
		</div>
	);
};

export default WhoViewed;
