'use client'
import React, { useEffect, useState } from "react";
import { Empty } from 'antd';
import { type Blog } from '@/lib/@types/api/blog';
import { getBlogList } from "@/lib/api/blog.ts";
import moment from "moment";
import { Link } from "@/navigation.ts";
import { RightOutlined } from "@ant-design/icons";
import { useTranslations } from "next-intl";


const FeatureBlogs: React.FC<{locale:string}> = ({locale}) => {
	const [blogList,setBlogList]=useState<Blog.BlogListItem[]>([])

	useEffect(()=>{
		// 请求博客
		getBlogList({lang_code:{lang_code:locale },	page: 1,
			limit: 3} as Blog.GetBlogListParams).then(res=>{
			setBlogList(res?.detail?.blog_list || [])
		})
	},[locale])

  const t=useTranslations()
	
	return < >
		<h2 className="heading6 mb-5">{t('nav.Featured Blogs')}</h2>
		{blogList.length > 0 && <div className=" flex flex-col gap-y-4">
			{blogList.map((item) => (
				<Link key={item.blog_id} href={'/blog/'+item.blog_slug} className="flex items-center  text-black hover:text-black hover:bg-gray-50 box-border p-2">

					<img src={item.blog_cover_origin} alt={item.blog_title} className="w-16 h-16 object-cover mx-3" />
					<div className="flex flex-col">
						<span>{item.blog_title}</span>
						<span>{moment(item.blog_upload_time).format("MMM YYYY")}</span>
					</div>

				</Link>

			))}
		</div> }

	</>


};

export default FeatureBlogs;
