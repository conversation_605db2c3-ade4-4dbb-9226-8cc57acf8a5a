"use client";
import { useState, useRef } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import Link from 'next/link';
import Image from 'next/image';
import { type Blog } from '@/lib/@types/api/blog';
import moment from 'moment';

interface BlogCategoryData {
  category: Blog.BlogCl;
  blogs: Blog.BlogListItem[];
}

interface HomeBlogProps {
  blogCategoriesData: BlogCategoryData[];
}

export default function HomeBlog({ blogCategoriesData }: HomeBlogProps) {
  const [activeIndex, setActiveIndex] = useState(0);
  const swiperRef = useRef<any>(null);
  const prevIndexRef = useRef(0);

  // 如果没有数据，显示默认内容
  if (!blogCategoriesData || blogCategoriesData.length === 0) {
    return null;
  }

  /* 切换 tab：外层 swiper 负责方向动画 */
  const handleTabClick = (index: number) => {
    if (!swiperRef.current) return;
    const direction = index > prevIndexRef.current ? 'next' : 'prev';
    swiperRef.current.slideTo(index, 500, direction === 'next');
    prevIndexRef.current = index;
    setActiveIndex(index);
  };

  return (
    <section className="bg-gray-50 py-[40px] md:py-[80px] lg:py-[120px]">
      {/* 标题 + tabs */}
      <div className="container px-4">
        <div className="text-center mb-8 md:mb-12">
          <h2 className="text-[28px] md:text-[36px] lg:text-[40px] font-bold text-[#222222] mb-3">
            PinShop近期动态
          </h2>
          <p className="text-gray-600 text-sm md:text-base max-w-2xl mx-auto">
            探索最新的行业洞察、产品更新和实用技巧
          </p>
        </div>

        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8 md:mb-12">
          <div className="flex gap-1 md:gap-2 overflow-x-auto pb-2 md:pb-0" style={{
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
            WebkitScrollbar: { display: 'none' }
          }}>
            {blogCategoriesData.map((item, idx) => (
              <button
                key={idx}
                onClick={() => handleTabClick(idx)}
                className={`relative cursor-pointer px-4 md:px-6 py-2.5 md:py-3 text-sm md:text-base font-medium whitespace-nowrap rounded-full transition-all duration-300
                  ${activeIndex === idx
                    ? 'bg-red-500 text-white shadow-lg shadow-red-500/25'
                    : 'bg-white text-[#222222] hover:bg-red-50 hover:text-red-500 border border-gray-200'
                  }`}
              >
                {item.category.cls_name}
              </button>
            ))}
          </div>

          <Link
            href="/blog"
            className="group relative inline-flex items-center text-sm md:text-base text-[#222222] hover:text-red-500 mt-4 md:mt-0 font-medium transition-colors"
          >
            了解更多
            <i className="ri-arrow-right-s-line ml-1 group-hover:translate-x-1 transition-transform"></i>
          </Link>
        </div>
      </div>

      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>

      {/* 外层 Swiper：每个 slide 是一个 tab 的内层 Swiper */}
      <div className="container px-4">
        <Swiper
          slidesPerView={1}
          onSwiper={(s) => (swiperRef.current = s)}
          allowTouchMove={false}
          speed={500}
        >
          {blogCategoriesData.map((categoryData, tabIdx) => (
            <SwiperSlide key={tabIdx}>
              {/* 内层 Swiper：当前 tab 的卡片 */}
              <Swiper
                spaceBetween={16}
                slidesPerView="auto"
                grabCursor
                className="tab-swiper !overflow-visible"
                breakpoints={{
                  320: {
                    slidesPerView: 1.1,
                    spaceBetween: 16,
                  },
                  480: {
                    slidesPerView: 1.3,
                    spaceBetween: 20,
                  },
                  640: {
                    slidesPerView: 2,
                    spaceBetween: 24,
                  },
                  1024: {
                    slidesPerView: "auto",
                    spaceBetween: 30,
                  },
                }}
              >
                {categoryData.blogs.map((blog, cardIdx) => (
                  <SwiperSlide key={cardIdx} className="!w-[300px] sm:!w-[320px] md:!w-[380px] lg:!w-[400px]">
                    <Link href={`/blog/${blog.blog_slug}`} className="block group">
                      <div className="bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-100">
                        <div className="relative w-full aspect-[16/10] overflow-hidden">
                          <Image
                            src={blog.blog_thumb || blog.blog_cover_origin || '/image/default-blog.jpg'}
                            alt={blog.blog_title}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                          {/* 渐变遮罩 */}
                          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                        </div>

                        <div className="p-4 sm:p-5">
                          <div className="flex items-center mb-3">
                            <div className="flex items-center text-gray-500">
                              <i className="ri-user-line mr-1.5 text-sm" />
                              <span className="text-xs font-medium">{blog.blog_author}</span>
                            </div>
                            <div className="mx-2 w-1 h-1 bg-gray-300 rounded-full"></div>
                            <span className="text-xs text-gray-500">
                              {moment(blog.blog_upload_time).format('MM-DD')}
                            </span>
                          </div>

                          <h3 className="text-sm sm:text-base font-semibold mb-2 line-clamp-2 text-[#222222] group-hover:text-red-500 transition-colors leading-relaxed">
                            {blog.blog_title}
                          </h3>

                          <p className="text-xs sm:text-sm text-gray-600 mb-3 line-clamp-2 leading-relaxed">
                            {blog.blog_excerpt}
                          </p>

                          {blog.blog_tag_list && blog.blog_tag_list.length > 0 && (
                            <div className="flex flex-wrap gap-1.5">
                              {blog.blog_tag_list.slice(0, 2).map((tag) => (
                                <span
                                  key={tag.tag_id}
                                  className="px-2 py-1 text-xs bg-red-50 text-red-600 rounded-full font-medium"
                                >
                                  {tag.tag_name}
                                </span>
                              ))}
                              {blog.blog_tag_list.length > 2 && (
                                <span className="px-2 py-1 text-xs bg-gray-100 text-gray-500 rounded-full">
                                  +{blog.blog_tag_list.length - 2}
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </Link>
                  </SwiperSlide>
                ))}
              </Swiper>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </section>
  );
}

