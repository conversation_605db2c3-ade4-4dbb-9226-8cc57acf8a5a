"use client";
import { useState, useRef } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import Link from 'next/link';
import Image from 'next/image';

/* ------------------ 静态数据 ------------------ */
const tabsData = [
    {
      title: '独立站运营',
      cards: new Array(6).fill(0).map((_, i) => ({
        cover: `/image/abouts/100${23 + (i % 2)}.png`,
        authorIcon: 'ri-user-line',
        authorName: `订单作者${i + 1}`,
        date: `2023-10-${15 - i}`,
        title: `订单功能更新 ${i + 1}`,
      })),
    },
    {
      title: '流量秘籍',
      cards: new Array(9).fill(0).map((_, i) => ({
        cover: `/image/abouts/100${23 + (i % 2)}.png`,
        authorIcon: 'ri-user-line',
        authorName: `商品作者${i + 1}`,
        date: `2023-09-${25 - i}`,
        title: `商品功能优化 ${i + 1}`,
      })),
    },
    {
      title: '库存管理',
      cards: new Array(4).fill(0).map((_, i) => ({
        cover: `/image/abouts/100${23 + (i % 2)}.png`,
        authorIcon: 'ri-user-line',
        authorName: `库存作者${i + 1}`,
        date: `2023-08-${20 - i}`,
        title: `库存策略升级 ${i + 1}`,
      })),
    },
  ];

export default function LatestUpdates() {
  const [activeIndex, setActiveIndex] = useState(0);
  const swiperRef = useRef<any>(null);
  const prevIndexRef = useRef(0);

  /* 切换 tab：外层 swiper 负责方向动画 */
  const handleTabClick = (index: number) => {
    if (!swiperRef.current) return;
    const direction = index > prevIndexRef.current ? 'next' : 'prev';
    swiperRef.current.slideTo(index, 500, direction === 'next');
    prevIndexRef.current = index;
    setActiveIndex(index);
  };

  return (
    <section className="bg-white py-[120px]">
      {/* 标题 + tabs */}
      <div className="container">
        <h2 className="text-[40px] font-bold text-[#222222] mb-16">
          PinShop近期动态
        </h2>

        <div className="flex items-center justify-between mb-8 border-b border-[rgba(34,34,34,0.15)]">
          <div className="flex gap-4">
            {tabsData.map((tab, idx) => (
              <div
                key={idx}
                onClick={() => handleTabClick(idx)}
                className={`relative cursor-pointer px-6 py-2 text-xl  text-[#222222] 
                  after:content-[''] after:absolute after:bottom-0 after:left-0 after:h-[2px] 
                  after:bg-red-500 after:transition-all after:duration-300
                  ${activeIndex === idx ? 'after:w-full text-red-500' : 'after:w-0'}`}
              >
                {tab.title}
              </div>
            ))}
          </div>

          <Link
            href="/about/honor"
            className="group relative text-xl text-[#222222] hover:text-[#555555]"
          >
            了解更多
            <span className="absolute bottom-0 left-0 w-0 h-[1px] bg-[#222222] transition-all duration-300 group-hover:w-full"></span>
            <i className="ri-arrow-right-s-line ml-1 align-middle"></i>
          </Link>
        </div>
      </div>

      {/* 外层 Swiper：每个 slide 是一个 tab 的内层 Swiper */}
      <div className="">
        <Swiper
          slidesPerView={1}
          onSwiper={(s) => (swiperRef.current = s)}
          allowTouchMove={false}
          speed={500}
        >
          {tabsData.map((tab, tabIdx) => (
            <SwiperSlide key={tabIdx}>
              {/* 内层 Swiper：当前 tab 的卡片 */}
              <Swiper
                spaceBetween={30}
                slidesPerView="auto"
                grabCursor
                className="tab-swiper"
              >
                {tab.cards.map((card, cardIdx) => (
                  <SwiperSlide key={cardIdx} style={{ width: 500 }}>
                    <div className="w-full">
                      <div className="relative w-full aspect-[500/379]">
                        <Image
                          src={card.cover}
                          alt={card.title}
                          fill
                          className="object-cover"
                        />
                      </div>

                      <div className="flex items-center mt-4">
                        <i className={`${card.authorIcon} mr-2`} />
                        <span className="text-sm font-medium">{card.authorName}</span>
                        <span className="text-xs text-gray-500 ml-2">{card.date}</span>
                      </div>

                      <h3 className="text-lg font-semibold mt-2 line-clamp-2">
                        {card.title}
                      </h3>
                    </div>
                  </SwiperSlide>
                ))}
              </Swiper>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </section>
  );
}

