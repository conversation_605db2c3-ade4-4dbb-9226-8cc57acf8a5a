"use client";
import { useState, useRef } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import Link from 'next/link';
import Image from 'next/image';
import { type Blog } from '@/lib/@types/api/blog';
import moment from 'moment';

interface BlogCategoryData {
  category: Blog.BlogCl;
  blogs: Blog.BlogListItem[];
}

interface HomeBlogProps {
  blogCategoriesData: BlogCategoryData[];
}

export default function HomeBlog({ blogCategoriesData }: HomeBlogProps) {
  const [activeIndex, setActiveIndex] = useState(0);
  const swiperRef = useRef<any>(null);
  const prevIndexRef = useRef(0);

  // 如果没有数据，显示默认内容
  if (!blogCategoriesData || blogCategoriesData.length === 0) {
    return null;
  }

  /* 切换 tab：外层 swiper 负责方向动画 */
  const handleTabClick = (index: number) => {
    if (!swiperRef.current) return;
    const direction = index > prevIndexRef.current ? 'next' : 'prev';
    swiperRef.current.slideTo(index, 500, direction === 'next');
    prevIndexRef.current = index;
    setActiveIndex(index);
  };

  return (
    <section className="bg-white py-[120px]">
      {/* 标题 + tabs */}
      <div className="container">
        <h2 className="text-[40px] font-bold text-[#222222] mb-16">
          PinShop近期动态
        </h2>

        <div className="flex items-center justify-between mb-8 border-b border-[rgba(34,34,34,0.15)]">
          <div className="flex gap-2 md:gap-4 overflow-x-auto pb-2 md:pb-0" style={{
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
            WebkitScrollbar: { display: 'none' }
          }}>
            {blogCategoriesData.map((item, idx) => (
              <div
                key={idx}
                onClick={() => handleTabClick(idx)}
                className={`relative cursor-pointer px-3 md:px-6 py-2 text-sm md:text-xl text-[#222222] whitespace-nowrap
                  after:content-[''] after:absolute after:bottom-0 after:left-0 after:h-[2px]
                  after:bg-red-500 after:transition-all after:duration-300
                  ${activeIndex === idx ? 'after:w-full text-red-500' : 'after:w-0'}`}
              >
                {item.category.cls_name}
              </div>
            ))}
          </div>

          <Link
            href="/blog"
            className="group relative text-base md:text-xl text-[#222222] hover:text-[#555555] hidden md:block"
          >
            了解更多
            <span className="absolute bottom-0 left-0 w-0 h-[1px] bg-[#222222] transition-all duration-300 group-hover:w-full"></span>
            <i className="ri-arrow-right-s-line ml-1 align-middle"></i>
          </Link>
        </div>
      </div>

      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>

      {/* 外层 Swiper：每个 slide 是一个 tab 的内层 Swiper */}
      <div className="">
        <Swiper
          slidesPerView={1}
          onSwiper={(s) => (swiperRef.current = s)}
          allowTouchMove={false}
          speed={500}
        >
          {blogCategoriesData.map((categoryData, tabIdx) => (
            <SwiperSlide key={tabIdx}>
              {/* 内层 Swiper：当前 tab 的卡片 */}
              <Swiper
                spaceBetween={30}
                slidesPerView="auto"
                grabCursor
                className="tab-swiper"
              >
                {categoryData.blogs.map((blog, cardIdx) => (
                  <SwiperSlide key={cardIdx} style={{ width: 500 }}>
                    <Link href={`/blog/${blog.blog_slug}`} className="block group">
                      <div className="w-full">
                        <div className="relative w-full aspect-[500/379]">
                          <Image
                            src={blog.blog_thumb || blog.blog_cover_origin || '/image/default-blog.jpg'}
                            alt={blog.blog_title}
                            fill
                            className="object-cover"
                          />
                        </div>

                        <div className="flex items-center mt-4">
                          <i className="ri-user-line mr-2" />
                          <span className="text-sm font-medium">{blog.blog_author}</span>
                          <span className="text-xs text-gray-500 ml-2">
                            {moment(blog.blog_upload_time).format('YYYY-MM-DD')}
                          </span>
                        </div>

                        <h3 className="text-lg font-semibold mt-2 line-clamp-2">
                          {blog.blog_title}
                        </h3>
                      </div>
                    </Link>
                  </SwiperSlide>
                ))}
              </Swiper>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </section>
  );
}

