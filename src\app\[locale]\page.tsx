import React from "react";
import { type Metadata } from "next";
import { type MyPageProps } from "@/lib/@types/base";
import { getBlogList, getBlogCls } from "@/lib/api/blog";
import { type Blog } from "@/lib/@types/api/blog";
import { getBasePageSeo } from "@/lib/api/seo";
import { generateSeo } from "@/lib/utils/seo";
import { unstable_setRequestLocale } from "next-intl/server";
import { defaultLocale, handleGraphqlLocale } from "@/lib/utils/util";
import { ProductCategoriesDocument } from "@/gql/graphql";
import HomeProducts from "@/components/ZC/HomeProducts";
import { getChannelLanguageMap } from "@/lib/api/channel";
import { fetchProductData } from "@/lib/api/product";
import BannerStatistics from "@/components/ZC/BannerStatistics";
import HomeSolution from "@/components/ZC/HomeSolution"
import CommonFooter from "@/components/Common/CommonFooter"
import HomePartner from "@/components/ZC/HomePartner"
import HomeOutSea from "@/components/ZC/HomeOutSea"
import HomeCases from "@/components/ZC/HomeCases"
import HomeReview, { HomeMarquee } from "@/components/ZC/HomeReview";
import HomeBlog from "@/components/ZC/HomeBlog";
/**
 * 生成页面的元数据
 * @param props 页面的属性，用于生成SEO信息
 * @returns 返回页面的元数据，包括SEO信息
 */
export const generateMetadata = async (props: MyPageProps): Promise<Metadata> => {
	// 获取基础页面的SEO数据
	const seo = await getBasePageSeo(props);
	// 生成最终的SEO信息，并返回
	return generateSeo(props, {
		...seo,
		ogType: "website",
	});
};

// 异步获取博客列表，确保是一个 Promise
export async function fetchBlogList(
	Params: Blog.GetBlogListParams,
): Promise<{ blogList: Blog.BlogListItem[]; blogCount: number }> {
	try {
		const res = await getBlogList(Params);
		return { blogList: res.detail.blog_list, blogCount: res.detail.blog_filter_count };
	} catch (error) {
		console.error("Error fetching blog list:", error);
		return { blogList: [], blogCount: 0 }; // 如果发生错误，返回一个空数组
	}
}

export default async function Page(props: MyPageProps) {
	unstable_setRequestLocale(props.params.locale);

	//获取商品
	const channel = (await getChannelLanguageMap())[defaultLocale];

	// 获取博客分类
	const blogCategories = await getBlogCls({
		lang_code: { lang_code: props.params.locale || defaultLocale }
	});

	// 获取每个分类的博客数据
	const blogDataPromises = blogCategories.slice(0, 5).map(async (category) => {
		const blogData = await getBlogList({
			lang_code: { lang_code: props.params.locale || defaultLocale },
			cls_ids: [category.cls_id],
			page: 1,
			limit: 6
		});
		return {
			category,
			blogs: blogData.detail?.blog_list || []
		};
	});

	const blogCategoriesData = await Promise.all(blogDataPromises);

	return (
		<>
			<BannerStatistics />
			<HomeSolution />
			<HomeCases />
			<HomeOutSea />
			<HomeReview />
			<HomeMarquee />
			<HomeBlog blogCategoriesData={blogCategoriesData} />
			<HomePartner />
			<CommonFooter />
		</>
	);
}
