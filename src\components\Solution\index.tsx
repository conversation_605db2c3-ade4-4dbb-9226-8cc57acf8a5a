'use client'
import React, { useState } from "react";
import { useModalInquiryContext } from "@/context/ModalInquiryContext";
import TwoColumnLayout from "./TwoColumnLayout";
import Advantage from "./Advantage";
import FAQ from "./FAQ";
import CustomerReviews from "./CustomerReviews";
import ProductFeatures from "./ProductFeatures";
import SolutionFooter from "./SolutionFooter";
import LoginModal from "../User/login-modal";
import useMenuMobile from "@/store/useMenuMobile";
import { useTranslations } from "next-intl";

const SolutionPage= ()=>{
	const { openMenuMobile, handleMenuMobile } = useMenuMobile();
	const { openModalInquiry } = useModalInquiryContext();	
    // 登录模态框状态管理
	const [openLoginModal, setOpenLoginModal] = useState(false);
	const [activeId, setActiveId] = useState(1); // 1: 登录, 2: 注册

    const t=useTranslations("solution");
    
    // 处理登录按钮点击
	const handleLoginClick = () => {
		setActiveId(1); // 设置为登录面板
		setOpenLoginModal(true);
		// 只有在移动端菜单打开时才关闭它
		if (openMenuMobile) {
			handleMenuMobile();
		}
	};

	// 处理注册按钮点击
	const handleRegisterClick = () => {
		setActiveId(2); // 设置为注册面板
		setOpenLoginModal(true);
		// 只有在移动端菜单打开时才关闭它
		if (openMenuMobile) {
			handleMenuMobile();
		}
	};

    return (
            <>
                <TwoColumnLayout
                    layout="left-text-right-image"
                    backgroundColor="bg-[#f8f4ec]"
                    textContent={{
                        title: t('Solutions1'),
                        description1: t('Solutions1-description'),
                        buttonText: t('Consult-now'),
                        buttonOnClick: () => openModalInquiry(false)
                    }}
                    imageContent={{
                        imageSrc: "/image/solution/solution1.webp",
                        imageAlt: "solution"
                    }}
                />

                <Advantage onLoginClick={handleLoginClick}/>

                <TwoColumnLayout
                    layout="left-image-right-text"
                    backgroundColor="bg-[#f8f4ec]"
                    textContent={{
                        title: t('Solutions1'),
                        description1: t('Solutions2-description-1'),
                        description2: t('Solutions2-description-2'),
                        buttonText: t('Free-trial'),
                        buttonOnClick: handleRegisterClick
                    }}
                    imageContent={{
                        imageSrc: "/image/solution/solution2.webp",
                        imageAlt: "solution"
                    }}
                />

                <TwoColumnLayout
                    layout="left-text-right-image"
                    backgroundColor="bg-[#ffffff]"
                    textContent={{
                        title: t('Solutions3'),
                        description1: t('Solutions3-description-1'),
                        description2: t('Solutions3-description-2'),
                        buttonText: t('Learn-more'),
                        buttonOnClick: () => openModalInquiry(false)
                    }}
                    imageContent={{
                        imageSrc: "/image/solution/solution3.webp",
                        imageAlt: "solution"
                    }}
                />
                
                <TwoColumnLayout
                    layout="left-image-right-text"
                    backgroundColor="bg-[#ddeeff]"
                    textContent={{
                        title: t('Solutions4'),
                        description1: t('Solutions4-description-1'),
                        description2: t('Solutions4-description-2'),
                        buttonText: t('Free-trial'),
                        buttonOnClick: handleRegisterClick
                    }}
                    imageContent={{
                        imageSrc: "/image/solution/solution4.webp",
                        imageAlt: "solution"
                    }}
                />

                <TwoColumnLayout
                    layout="left-text-right-image"
                    backgroundColor="bg-[#ffffff]"
                    textContent={{
                        title: t('Solutions5'),
                        description1: t('Solutions5-description'),
                        buttonText: t('Consult-immediately'),
                        buttonOnClick: () => openModalInquiry(false)
                    }}
                    imageContent={{
                        imageSrc: "/image/solution/solution3.webp",
                        imageAlt: "solution"
                    }}
                />

                <FAQ onLoginClick={handleLoginClick} />
                <CustomerReviews />
                <ProductFeatures buttonOnClick={handleRegisterClick}/>
                <SolutionFooter />
                {/* 登录模态框 */}
                <LoginModal
                    openModal={openLoginModal}
                    setOpenModal={setOpenLoginModal}
                    activeId={activeId}
                    setActiveId={setActiveId}
                />
            </>
        )
    }

export default SolutionPage
