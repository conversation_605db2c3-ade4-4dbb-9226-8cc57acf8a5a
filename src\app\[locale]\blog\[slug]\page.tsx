import moment from "moment";
import { Avatar } from "antd";
import React, { useEffect } from "react";
import { Link } from "@/navigation";
import { getBlogPageSeo } from "@/lib/api/seo";
import { generateSeo } from "@/lib/utils/seo";
import { type SlugMyPageProps } from "@/lib/@types/base";
import { getBlogCls, getBlogDetail } from "@/lib/api/blog";
import { SocialShare } from "@/components/Other/Share";
import { fetchBlogList } from "@/app/[locale]/page";
import BlogArticle from "@/components/Blogs/BlogArticle";
import FollowUs from "@/components/Social/FollowUs";
import { ClockCircleFilled } from "@ant-design/icons";
import BlogComment from "@/components/Blogs/BlogComment.tsx";
import FeatureBlogs from "@/components/Blogs/FeaturedBlogs.tsx";
import { NotFouned } from "@/components/404";
import { getTranslations, unstable_setRequestLocale } from "next-intl/server";

interface BlogDetailPageProps {
	params: {
		locale: string;
		slug: string;
	};
}

// 动态生成SEO的metadata
export const generateMetadata = async (props: SlugMyPageProps) => {
	const seo = await getBlogPageSeo(props);
	// @ts-ignore
	return generateSeo(props, {
		...seo,
		currentPath: `/blog/${props.params.slug}`,
		ogType: "article",
	});
};

// 博客详情页面组件
export default async function BlogDetailPage({ params }: BlogDetailPageProps) {
	const { slug, locale } = params;
	// 获取博客详情
	const blog = await getBlogDetail(slug, locale);
	if (!blog) {
		return <NotFouned link={"/blog"} />;
	}

	// 获取相关文章
	const { blogList } = await fetchBlogList({
		lang_code: { lang_code: params.locale },
		page: 1,
		limit: 6,
		cls_slug_in: [{ slug: blog?.blog_classification_list[0]?.cls_slug || "default" }],
	});
	let preSlug = { slug: "", title: "", upload_time: "" };
	let nxtSlug = { slug: "", title: "", upload_time: "" };
	// console.log(blogList,2323);
	if (blogList.length > 0) {
		const index = blogList.findIndex((item) => item.blog_slug === slug);
		// console.log(index, "index");

		if (index > -1) {
			// 上一篇（循环到最后）
			const prevIndex = (index - 1 + blogList.length) % blogList.length;
			preSlug = {
				slug: blogList[prevIndex].blog_slug,
				title: blogList[prevIndex].blog_title,
				upload_time: blogList[prevIndex].blog_upload_time,
			};

			// 下一篇（循环到第一）
			const nextIndex = (index + 1) % blogList.length;
			nxtSlug = {
				slug: blogList[nextIndex].blog_slug,
				title: blogList[nextIndex].blog_title,
				upload_time: blogList[nextIndex].blog_upload_time,
			};
		}
	}

	const clsList = await getBlogCls({ lang_code: { lang_code: params.locale } });

	unstable_setRequestLocale(params.locale);
	const t = await getTranslations();
	const cleanArticleBody = blog.blog_content
		.replace(/<[^>]+>/g, "")
		.replace(/\n+/g, " ")
		.replace(/\s+/g, " ")
		.trim();

	// 构建结构化数据
	const structuredData = {
		"@context": "https://schema.org",
		"@type": "BlogPosting",
		"@id": `${process.env.NEXT_PUBLIC_SITE_URL}/blog/${blog.slug}`,
		mainEntityOfPage: {
			"@type": "WebPage",
			"@id": `${process.env.NEXT_PUBLIC_SITE_URL}/blog/${blog.slug}`,
		},
		headline: blog.blog_title,
		description: blog.blog_seoDescription || blog.blog_excerpt,
		image: blog.cover_url,
		keywords: blog.blog_seoKeyword.join(", "),
		datePublished: blog.update_time,
		dateModified: blog.update_time,
		author: {
			"@type": "Person",
			name: blog.blog_author,
		},
		publisher: {
			"@type": "Organization",
			name: process.env.NEXT_PUBLIC_COMPANY_NAME,
			logo: {
				"@type": "ImageObject",
				url: `${process.env.NEXT_PUBLIC_SITE_URL}/image/img/logo.png`,
			},
		},
		articleSection:
			blog.blog_classification_list && blog.blog_classification_list.length > 0
				? blog.blog_classification_list[0].cls_name
				: "",
		articleBody: cleanArticleBody,
	};
	return (
		<>
			<script
				id={`blog-structured-data-${slug}`}
				type="application/ld+json"
				dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
			/>
			<div className="">
				{/* 面包屑导航 */}
				{/* <div className="container1730 pb-12 pt-12">
					<div className="flex gap-x-2  text-sm text-gray-500">
						<Link href="/" className="text-gray-500">
							{t("menu.Home")}
						</Link>
						<span className="">/</span>
						<Link href="/blog" className="text-gray-500">
							{t("menu.Blog")}
						</Link>
						<span className="">/</span>
						<Link href={"/blog/" + params.slug} className="text-gray-500">
							{params.slug}
						</Link>
					</div>
				</div> */}

				<div className="container mx-auto   pb-12 pt-[60px] md:pt-[130px]">
					<BlogArticle
						content={blog?.blog_content || ""}
						blogList={blogList}
						blog={blog}
						clsList={clsList}
					></BlogArticle>

					{/* 上一篇和下一篇博客导航 */}
					{/* 上一篇和下一篇博客导航 */}
					{(preSlug.slug || nxtSlug.slug) && (
						<nav className="my-8 flex items-center justify-between gap-8 border-b border-t border-gray-100 py-10">
							{/* Previous Post */}
							{preSlug.title ? (
								<Link
									href={`/blog/${preSlug.slug}`}
									className="group flex flex-1 items-center space-x-4 rounded-lg p-4 transition-all duration-300 hover:bg-gray-50"
								>
									<div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-gray-50 group-hover:bg-white group-hover:shadow-sm">
										<svg
											className="h-5 w-5 text-gray-400 transition-colors group-hover:text-blue-500"
											fill="none"
											viewBox="0 0 24 24"
											stroke="currentColor"
										>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth={1.5}
												d="M15 19l-7-7 7-7"
											/>
										</svg>
									</div>
									<div className="min-w-0 flex-1">
										<p className="mb-2 text-xs font-medium uppercase tracking-wider text-gray-500">
											Previous Post
										</p>
										<h4 className="line-clamp-1 truncate text-base font-medium text-gray-900 group-hover:text-blue-600 max-md:hidden">
											{preSlug.title}
										</h4>
										<time className="mt-1 text-sm text-gray-500 max-md:hidden">
											{moment(preSlug?.upload_time).format("MMM DD, YYYY")}
										</time>
									</div>
								</Link>
							) : (
								<div className="flex-1 max-md:hidden" />
							)}

							{/* Next Post */}
							{nxtSlug.title ? (
								<Link
									href={`/blog/${nxtSlug.slug}`}
									className="group flex flex-1 items-center space-x-4 rounded-lg p-4 text-right transition-all duration-300 hover:bg-gray-50"
								>
									<div className="min-w-0 flex-1">
										<p className="mb-2 text-xs font-medium uppercase tracking-wider text-gray-500">
											Next Post
										</p>
										<h4 className="line-clamp-1 truncate text-base font-medium text-gray-900 group-hover:text-blue-600 max-md:hidden">
											{nxtSlug.title}
										</h4>
										<time className="mt-1 text-sm text-gray-500 max-md:hidden">
											{moment(nxtSlug?.upload_time).format("MMM DD, YYYY")}
										</time>
									</div>
									<div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-gray-50 group-hover:bg-white group-hover:shadow-sm">
										<svg
											className="h-5 w-5 text-gray-400 transition-colors group-hover:text-blue-500"
											fill="none"
											viewBox="0 0 24 24"
											stroke="currentColor"
										>
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5l7 7-7 7" />
										</svg>
									</div>
								</Link>
							) : (
								<div className="flex-1 max-md:hidden" />
							)}
						</nav>
					)}
				</div>
				<div className="container">
					<div className="heading6 mb-5">{t("nav.Featured Blogs")}</div>
				</div>
				<div className="container mb-6 grid w-full    grid-cols-3 gap-x-10 gap-y-5 max-md:grid-cols-1">
					{blogList.map((item) => (
						<Link
							key={item.blog_id}
							href={"/blog/" + item.blog_slug}
							className="group flex w-full flex-col overflow-hidden transition-all duration-300 hover:-translate-y-1 max-md:mb-8"
						>
							{/* 图片容器 */}
							<div className="relative mb-4 aspect-[16/9] overflow-hidden rounded-sm">
								<img
									src={item.blog_cover_origin}
									alt={item.blog_title}
									className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-105"
								/>
								{/* 渐变遮罩 */}
								<div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
							</div>

							{/* 文字内容 */}
							<div className="flex flex-col px-2">
								<h3 className="mb-2 line-clamp-2 text-lg font-medium text-gray-900 transition-colors group-hover:text-blue-600">
									{item.blog_title}
								</h3>

								<div className="flex items-center gap-4 text-sm text-gray-500">
									{/* 发布时间 */}
									<time className="flex items-center gap-1">
										<svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth={2}
												d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
											/>
										</svg>
										{moment(item.blog_upload_time).format("MMM DD, YYYY")}
									</time>
								</div>

								{/* 文章摘要 */}
								<p className="mt-3 line-clamp-2 text-sm text-gray-600">{item.blog_excerpt}</p>
							</div>
						</Link>
					))}
				</div>
			</div>
		</>
	);
}
