import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import pkg from '@next/env';
const { loadEnvConfig } = pkg;
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 加载环境变量配置
loadEnvConfig(process.cwd());
export const getTranslationList = async () => {
	const languages = JSON.parse(fs.readFileSync(path.join(__dirname, '../language.json'), 'utf8'));

	// 检查otherLang文件夹是否存在，如果不存在则创建
	const dirPath = path.join(__dirname, '../src/lib/otherLang');
	if (!fs.existsSync(dirPath)) {
		fs.mkdirSync(dirPath, { recursive: true });
	}

	for (const lang of languages) {
		// console.log(`--------------开始写入翻译数据：`,lang.code);
		const locale = lang.code;
		if (locale === 'zh-Hans') continue; // 跳过中文
		let msg = {};
		msg = await fetchAllData(locale, 1, msg);
		// 将msg写入以locale命名的JSON文件中
		const filePath = path.join(dirPath, `${locale}.json`);
		fs.writeFileSync(filePath, JSON.stringify(msg, null, 2), 'utf8');
		// console.log(`--------------写入翻译数据完成：`,lang.code);

	}
};

export async function fetchAllData(locale, page, msg ) {
	const res = await get_dict(locale, page); // 获取当前页数据

	if (res ) {
		msg=Object.assign(msg,res.data)
	}
	if(res&&res.total>page){
		page++
		await fetchAllData(locale,page,msg)
	}
	// 如果达到最后一页，返回完整结果
	return msg;
}

export  const  get_dict=async (locale,page)=>{
	try{

		const res = await fetch(`${process.env.NEXT_PUBLIC_AI_URL}/api/v1/ml_web/get_translate_cursor_dict`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json', // 指定请求体的格式为JSON
			},
			body: JSON.stringify({
				language: locale,
				key_list:[],
				domain:new URL(process.env.NEXT_PUBLIC_SITE_URL||"").hostname,
				page: page,
				max_size_kb: 200 //切片大小300kb
			})
		}).then((r) => r.json() );

		if(res.code===200){
			return res.result
		}else{
			return null
		}
	}catch (e){
		return  null
	}


}


 getTranslationList()
