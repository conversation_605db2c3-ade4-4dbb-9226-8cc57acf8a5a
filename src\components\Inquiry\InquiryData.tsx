"use client";
import { defaultLocale, deleteInquiryByProductId, getInquiry } from "@/lib/utils/util";
import { Link } from "@/navigation";
import React, { useEffect, useState } from "react";
import EmptyState from "../EmptyState";
import { useLocale, useTranslations } from "next-intl";
import ContactForm from "../InquryForm/ContactForm";
import { getImagesForProduct } from "../Product/imageGallery";
import { HomeTaile } from "../Contact/ConcatPage";

 function InquiryData() {
	const [InquiryList, setInquiryList] = useState(getInquiry());
	const t = useTranslations();

	let locale = useLocale();
	console.log("cartList9999", InquiryList);

    function remove(id:string){

        console.log(id);
        deleteInquiryByProductId(id)


    }

    useEffect(() => {
		const handleResize = () => {
		let Inquirydata=getInquiry()
		setInquiryList(Inquirydata);
		  };
		  handleResize();
		  window.addEventListener("storage", handleResize);
	  
		  // Cleanup function to remove event listener
		  return () => window.removeEventListener("storage", handleResize);
	}, []);
	return (
		<div>
      <HomeTaile msg={t("nav.InquiryProducts")} />
			{InquiryList.length ? (
				<div className="container my-32">
					<ul className="divide-y divide-gray-200 border-y border-gray-200 mb-5">
						{InquiryList.map((item, index) => {
							return (
								<li key={index} className="flex py-6 sm:py-10">
									<div className="flex-shrink-0">
										<img
											src={getImagesForProduct(item.variant.product)[0]?.url|| "/image/default-image.webp"}
											alt={item.name}
											className="h-30 w-24 rounded-md object-cover object-center sm:h-48 sm:w-48"
										/>
									</div>
									<div className="ml-4 flex flex-1 flex-col justify-between sm:ml-6">
										<div className="relative pr-9 sm:grid sm:grid-cols-2 sm:gap-x-6 sm:pr-0">
											<div>
												<div className="flex justify-between">
													<h3 className="text-sm">
														<Link
															href={`/product/${item.slug}`}
															className="font-medium text-gray-700 hover:text-gray-800"
														>
															{item.name}
														</Link>
													</h3>
												</div>
                                                <div className="mt-1 flex text-sm">
													<div className="text-gray-500 flex ">{t("product.Name")} <span className="max-md:hidden">:</span> </div>
													<p className=" border-l border-gray-200  text-gray-500">{locale===defaultLocale?(item?.variant?.product?.name):(item?.variant?.product?.translation?.name)||(item?.variant?.product?.name)||""}</p>
												</div>
												<div className="mt-1 flex text-sm">
													<div className="text-gray-500  flex ">{t("product.Quantity")} <span className="max-md:hidden">:</span></div>
                                                    <p className="  border-gray-200  text-gray-500">
														{item.quantity || 1}
													</p>
												</div>
												{/* <p className="mt-1 text-sm font-medium text-gray-900">{item.regularPrice}</p> */}
											</div>

											<div className="mt-4 sm:mt-0 sm:pr-9">
												<div className="absolute right-0 top-0">
													<button
														type="button"
														className="-m-2 inline-flex p-2 text-gray-400 hover:text-gray-500"
														onClick={() => remove(item.variant.product.id)}
													>
														<span className="sr-only">{t("product.Remove")}</span>
														<svg
															className="h-5 w-5"
															viewBox="0 0 20 20"
															fill="currentColor"
															aria-hidden="true"
														>
															<path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z"></path>
														</svg>
													</button>
												</div>
											</div>
										</div>
									</div>
								</li>
							);
						})}
					</ul>
					<div>
						<ContactForm
							locale={locale}
							title={t("base.getInstantQuoteAndTS")}
							inquiry={InquiryList}
							source="inquiry"
							// @ts-ignore
							titleContent={t("form.fillOutIn")}
							className="!py-0 "
							innerClassName="!shadow-none !rounded-none"
						></ContactForm>
					</div>
				</div>
			) : (
				<EmptyState />
			)}
		</div>
	);
}


export default InquiryData;