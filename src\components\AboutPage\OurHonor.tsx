// components/HonorSection.tsx
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Navigation } from "swiper/modules";
import 'swiper/css';
import Image from 'next/image';
import { useRef, useState } from 'react';
import SwiperCore from 'swiper';
import Link from 'next/link'; // 添加 Link 组件导入


// 奖项信息（可抽离到 JSON 或 CMS）
const honors = [
  {
    id: 1,
    title: 'Google 2021 出海技术创新奖',
    img: '/image/abouts/10001.png',
  },
  {
    id: 2,
    title: '36Kr WISE 新经济之王 · 跨境服务领域年度企业',
    img: '/image/abouts/10002.png',
  },
  {
    id: 3,
    title: '2023 年度最佳独立站合作伙伴',
    img: '/image/abouts/10003.png',
  },
  {
    id: 4,
    title: 'InfoQ 中国企业数智化创新 TOP50',
    img: '/image/abouts/10004.webp',
  },
  {
    id: 5,
    title: 'InfoQ 中国企业数智化创新 TOP50',
    img: '/image/abouts/10005.webp',
  },
  {
    id: 6,
    title: 'InfoQ 中国企业数智化创新 TOP50',
    img: '/image/abouts/10006.webp',
  },
  {
    id: 7,
    title: '36氪WISE新经济之王-跨境服务领域年度企业',
    img: '/image/abouts/10007.png',
  },
  {
    id: 8,
    title: 'InfoQ 中国企业数智化创新 TOP50',
    img: '/image/abouts/10008.jpeg',
  },
  {
    id: 9,
    title: 'InfoQ 中国企业数智化创新 TOP50',
    img: '/image/abouts/10009.png',
  },
];

export default function HonorSection() {
  // 创建 Swiper 实例引用
  const swiperRef = useRef<any>(null);

  return (
    <section className="bg-[#cae4ff]">
      <div className="mx-auto max-w-[1920px] md:px-[160px] px-[24px] md:py-[120px] py-[60px]">
        {/* 修改后的标题部分 */}
        <div className="honor_head flex justify-between items-center mb-10">
          <div className="inner_title">
            <div className="title md:text-[40px] text-2xl font-bold text-[#222222]">我们的荣誉</div>
          </div>
          <div className="group">
            <Link 
              href="/about/honor" 
              className="relative inline-block md:text-xl text-lg text-[#222222] hover:text-[#555555]"
            >
              了解更多
              <span className="absolute bottom-0 left-0 w-0 h-[1px] bg-[#222222] transition-all duration-300 group-hover:w-full"></span>
              <i className="ri-arrow-right-s-line text-xl group-hover:opacity-100 opacity-0 duration-300"></i>
            </Link>
          </div>
        </div>

        <div className="relative">
          <Swiper
             modules={[Autoplay]}
             spaceBetween={20}
             slidesPerView={1}
             slidesPerGroup={1}                  
             onSwiper={(swiper) => (swiperRef.current = swiper)}
             breakpoints={{
               640: { slidesPerView: 1, slidesPerGroup: 1 },
               1024: { slidesPerView: 2, slidesPerGroup: 2 },
               1440: { slidesPerView: 4, slidesPerGroup: 4 }, // 👈 同步设置
             }}
          >
            {honors.map((item) => (
              <SwiperSlide key={item.id}>
                <div className="flex flex-col items-center">
                  <div className="relative h-[525px] md:w-[373px] w-full overflow-hidden group">
                    <Image
                      src={item.img}
                      alt={item.title}
                      fill
                      sizes="(max-width: 768px) 100vw, 384px"
                      className="object-cover transition-transform duration-500 group-hover:scale-110"
                    />
                  </div>
                  <p className="w-full mt-6 text-base text-[#222222]">
                    {item.title}
                  </p>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
          
          {/* 使用 RemixIcon 的自定义按钮 */}
          <button 
            className=" absolute -left-[70px] top-1/2 z-10 -translate-y-1/2 transform h-12 w-12 rounded-full bg-white/40 p-2 shadow-md hover:bg-gray-100"
            onClick={() => swiperRef.current?.slidePrev()}
          >
            <i className="ri-arrow-left-s-line text-2xl text-gray-700"></i>
          </button>
          <button 
            className="absolute -right-[80px] top-1/2 z-10 -translate-y-1/2 transform h-12 w-12 rounded-full bg-white/40 p-2 shadow-md hover:bg-gray-100"
            onClick={() => swiperRef.current?.slideNext()}
          >
            <i className="ri-arrow-right-s-line text-2xl text-gray-700"></i>
          </button>
        </div>
      </div>
    </section>
  );
}