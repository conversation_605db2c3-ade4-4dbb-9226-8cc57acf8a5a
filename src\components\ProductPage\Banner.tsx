import Image from "next/image";

const Banner = () => {
	return (
		<div className="bg-[#f8f4ec] pt-[118px]">
			{/* 采用 flex-col 以保证移动端垂直布局，lg 以上保持左右排列 */}
			<div className="container flex flex-col-reverse items-center justify-between gap-12 py-[60px] lg:flex-row lg:gap-0">
				<div className="text-[#222]">
					<p>帮助商家链接全球消费者</p>
					<h1 className="mt-[16px] text-[48px] font-bold">品店PinShop跨境电商系统</h1>
					<p className="mt-[24px] text-[20px]">全链路“售卖-营销-管理”能力支持，开店功能全覆盖</p>
					<div className="mt-[40px] flex gap-[16px]">
						<button className="h-[46px] rounded-[4px] border bg-[#000] px-[24px] text-white">开始建站</button>
						<button className="h-[46px] rounded-[4px] border border-[#000] bg-transparent px-[24px] transition-colors hover:bg-gray-100">
							开店咨询
						</button>
					</div>
				</div>
				<div className="w-full lg:w-1/2">
					<Image
						src="/image/product/6571744963050.png"
						alt="品店PinShop 跨境电商系统 banner"
						width={1000}
						height={1000}
						sizes="(max-width: 1024px) 100vw, 50vw"
						className="h-auto w-full object-contain"
						priority
					/>
				</div>
			</div>
		</div>
	);
};

export default Banner;
