import TypographyPlugin from "@tailwindcss/typography";
import FormPlugin from "@tailwindcss/forms";
import ContainerQueriesPlugin from "@tailwindcss/container-queries";
import { type Config } from "tailwindcss";
const config: Config = {
	content: [
		"./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
		"./src/components/**/*.{js,ts,jsx,tsx,mdx}",
		"./src/app/**/*.{js,ts,jsx,tsx,mdx}",
	],
	mode: "jit",
	jit: true,
	theme: {
		extend: {
			fontFamily: {
				sans: ["sans"],
				HarmonyOS: ["HarmonyOS"],
			},
			backgroundImage: {
				"gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
				"gradient-conic": "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
			},
			// 版心1577
			width: {
				main: "1577px",
			},
			maxWidth: {
				main: "1577px",
			},
			colors: {
				// 描述
				descColor: "#4C5D66",
				// 浅标题
				titleLight: "#222222",
				// 浅色调
				mainPrimaryLight: "#f8f4ec",
				// 中色调
				mainPrimaryMedium: "#FFA180",
				// 辅助色
				mainPrimary: "#f30006",
				main: "#000",
				mainColor: "#dc2626",
				white: "#ffffff",
				ddd: "#a1a1a1",
				themePrimary50: "#E6F0FF",
				themePrimary100: "#CCE1FF",
				themePrimary200: "#99C2FF",
				themePrimary300: "#66A2FF",
				themePrimary400: "#3383FF",
				themePrimary500: "#0064FF",
				themePrimary600: "#4d85f5",
				themePrimary700: "#004EAD",
				themePrimary800: "#00428A",
				themePrimary900: "#003667",
				themeSecondary50: "#fafafa",
				themeSecondary100: "#f4f4f5",
				themeSecondary200: "#e4e4e7",
				themeSecondary300: "#d4d4d8",
				themeSecondary400: "#a1a1aa",
				themeSecondary500: "#71717A",
				themeSecondary600: "#52525b",
				themeSecondary700: "#3f3f46",
				themeSecondary800: "#27272a",
				themeSecondary900: "#18181b",
				themeSuccess50: "#ecfdf5",
				themeSuccess100: "#d1fae5",
				themeSuccess200: "#a7f3d0",
				themeSuccess300: "#6ee7b7",
				themeSuccess400: "#34d399",
				themeSuccess500: "#10b981",
				themeSuccess600: "#059669",
				themeSuccess700: "#047857",
				themeSuccess800: "#065f46",
				themeSuccess900: "#064e3b",
				themeWarning50: "#fffbeb",
				themeWarning100: "#fef3c7",
				themeWarning200: "#fde68a",
				themeWarning300: "#fcd34d",
				themeWarning400: "#fbbf24",
				themeWarning500: "#f59e0b",
				themeWarning600: "#d97706",
				themeWarning700: "#b45309",
				themeWarning800: "#92400e",
				themeWarning900: "#78350f",
				themeDanger50: "#fef2f2",
				themeDanger100: "#fee2e2",
				themeDanger200: "#fecaca",
				themeDanger300: "#fca5a5",
				themeDanger400: "#f87171",
				themeDanger500: "#ef4444",
				themeDanger600: "#dc2626",
				themeDanger700: "#b91c1c",
				themeDanger800: "#991b1b",
				themeDanger900: "#7f1d1d",
				themeInfo50: "#ecfeff",
				themeInfo100: "#cffafe",
				themeInfo200: "#a5f3fc",
				themeInfo300: "#67e8f9",
				themeInfo400: "#22d3ee",
				themeInfo500: "#06b6d4",
				themeInfo600: "#0891b2",
				themeInfo700: "#0e7490",
				themeInfo800: "#155e75",
				themeInfo900: "#164e63",
				themeLightWhite: "#1B2631",
				themeRgbaColor: "rgba(255, 255, 255, 0.7)",
				themeRgbaColorTwo: "rgba(255, 255, 255, 0.2)",
				themeGraylight: "#6B7280",
				themBlack: "#18181B",
				themWhite: "#F1F5F9",
				themeGray: "#64748B",
				thmeBlackLight: "#1F2937",
				// checkoutflow-colors
				themeLightGray: "#F4F7FB",
				themeDark: "#2E4053",
				orangeOne: "#FFAB90",
				orangeTwo: "#FF4307",
				blueOne: "#75B1FF",
				blueTwo: "#056BF1",
				purpleLight: "#B593FF",
				purpleDark: "#6726F2",
				primaryGreen: "#4AAD4E",
				greenLight: "#67E5A3",
				greenDark: "#08994D",
				yellowLight: "#FFD470",
				yellowDark: "#FB9600",
				redLight: "#FF7E85",
				redDark: "#FF2632",
				themeNevyLight: "#5D6D7E",
				themeRedLight: "#DE0404",
			},
			boxShadow: {
				mainShadow: "0 0px 28px 4px rgb(0 0 0 / 0.1), 0 13px 8px -6px rgb(0 0 0 / 0.1)",
				card: "-1px -1px 10px 0px rgba(145, 145, 145, 0.2)",
			},
			animation: {
				"pulse-slow": "pulse 2s infinite",
			},
		},
		container: {
			padding: {
				DEFAULT: "16px",
			},
		},
	},
	plugins: [TypographyPlugin, FormPlugin, ContainerQueriesPlugin],
};

export default config;
