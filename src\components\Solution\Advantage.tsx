'use client'
import React from "react";
import { useModalInquiryContext } from "@/context/ModalInquiryContext";
import { useTranslations } from "next-intl";

const Advantage= ({onLoginClick}:{onLoginClick:()=>void})=>{
    const t = useTranslations("solution");

    return (
            <div className="bg-[#ffa180] w-screen py-20">
                <div className="container mx-auto py-10 max-md:py-0">
                    {/* 主标题 */}
                    <h2 className="text-4xl font-bold text-[#222222] text-center mb-16">
                        {t('AdvantageTitle')}
                    </h2>

                    {/* 三个优势列 */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-12 mb-16">
                        {/* 优势1：专业建站系统 */}
                        <div className="text-center">
                            <div className="w-16 h-16 bg-white rounded-full border border-gray-200 flex items-center justify-center mx-auto mb-6">
                                <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7l3-3 3 3m0 6l-3 3-3-3" />
                                </svg>
                            </div>
                            <h3 className="text-xl font-bold text-[#222222] mb-4">
                                {t("Advantage1")}
                            </h3>
                            <p className="text-[#222222] leading-relaxed">
                                {t("Advantage1-desc")}
                            </p>
                        </div>

                        {/* 优势2：资深团队支持 */}
                        <div className="text-center">
                            <div className="w-16 h-16 bg-white rounded-full border border-gray-200 flex items-center justify-center mx-auto mb-6">
                                <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                            </div>
                            <h3 className="text-xl font-bold text-[#222222] mb-4">
                                {t('Advantage2')}
                            </h3>
                            <p className="text-[#222222] leading-relaxed">
                                {t('Advantage2-desc')}
                            </p>
                        </div>

                        {/* 优势3：跨境全环节助力 */}
                        <div className="text-center">
                            <div className="w-16 h-16 bg-white rounded-full border border-gray-200 flex items-center justify-center mx-auto mb-6">
                                <svg className="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                            </div>
                            <h3 className="text-xl font-bold text-[#222222] mb-4">
                                {t('Advantage3')}
                            </h3>
                            <p className="text-[#222222] leading-relaxed">
                                {t('Advantage3-desc')}
                            </p>
                        </div>
                    </div>

                    {/* 免费试用按钮 */}
                    <div className="text-center">
                        <button 
                            className="bg-[#222222] text-white px-8 py-3 rounded-sm font-medium hover:bg-gray-700 transition-colors"
                            onClick={onLoginClick}
                        >
                            {t('Free-trial')}
                        </button>
                    </div>
                </div>
            </div>
        )
    }

export default Advantage
