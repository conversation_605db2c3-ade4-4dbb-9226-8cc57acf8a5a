
import React from 'react';
import { getBlogCls, getBlogTags } from "@/lib/api/blog";
import Breadcrumb from '@/components/Breadcrumb/Breadcrumb';
import BlogList from '@/components/Blogs/BlogList';
import RecentPosts from '@/components/Blogs/RecentPosts';
import Categories from '@/components/Blogs/Categories';
import TagsCloud from '@/components/Blogs/Tags';
// import HandlePagination from '@/components/Other/HandlePagination';
import {type Blog } from '@/lib/@types/api/blog';
import {type MyPageProps } from '@/lib/@types/base';
import { fetchBlogList } from "@/app/[locale]/page";
import { Link } from "@/navigation";
import BlogSeach from "@/components/Blogs/BlogSeach";
// import { Pagination } from 'antd';

// 使用async函数获取数据并渲染
export default async function BlogTag({ params ,searchParams}: MyPageProps) {
	const limit = 12;
	let pageCount = 1;
	const page = 1;

	// 从 API 获取博客列表和分类
	const { blogList, blogCount } = await fetchBlogList({
		lang_code: { lang_code: params.locale },
		page,
		limit,
		tag_slug_in:[{slug:params.slug}]
	});


	// if (blogCount) {
	// 	pageCount = Math.ceil(blogCount / limit);
	// }
	//
	const clsList = await getBlogCls({ lang_code: { lang_code: params.locale }});
	const tagList = await getBlogTags({ lang_code: { lang_code: params.locale }});

	//
	// const handlePageChange = async (selected: number) => {
	// 	// 页码更改处理逻辑
	// 	console.log("Selected page:", selected);
	// };

	return (
		<>
			<div id="header" className="relative w-full">
				<Breadcrumb heading="Blog List" subHeading="Blog List" />
			</div>
			<section className="blog list md:py-20 py-10">
				<div className="container">
					<div className="flex justify-between max-xl:flex-col gap-y-12">
						<div className="left xl:w-3/4 xl:pr-2 ">
							<BlogList blogList={blogList} />
							{/*{pageCount > 1 && (*/}
							{/*	<div className="list-pagination w-full flex items-center md:mt-10 mt-6">*/}
							{/*		<Pagination defaultCurrent={1} total={pageCount} onChange={handlePageChange} />								</div>*/}
							{/*)}*/}
						</div>
						<div className="right xl:w-1/4 xl:pl-[52px]">
							<BlogSeach></BlogSeach>
							<RecentPosts blogList={blogList} />
							<Categories clsList={clsList} />
							<TagsCloud tagList={tagList} />
						</div>
					</div>
				</div>
			</section>
		</>
	);
}


