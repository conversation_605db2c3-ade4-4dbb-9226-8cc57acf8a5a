import React, { useState, useEffect } from 'react'; // 添加 useEffect
import { Swiper, SwiperSlide } from 'swiper/react';
import { FreeMode } from 'swiper/modules'; // 只保留 FreeMode 如果需要内容滑动自由模式（可选）
import 'swiper/css';
import 'swiper/css/free-mode';
import { Link } from '@/navigation';
import LoginModal from "../User/login-modal";
import useMenuMobile from "@/store/useMenuMobile";

export default function ProductFunction() {
  const [activeIndex, setActiveIndex] = useState(0); // 管理当前 active tab 索引
  const [mainSwiper, setMainSwiper] = useState<any>(null); // 内容 Swiper 实例

  const { openMenuMobile, handleMenuMobile } = useMenuMobile();
  const [activeId, setActiveId] = useState(1); // 1: 登录, 2: 注册
  const [openLoginModal, setOpenLoginModal] = useState(false);


  // 处理注册按钮点击
  const handleRegisterClick = () => {
    setActiveId(2); // 设置为注册面板
    setOpenLoginModal(true);
    // 只有在移动端菜单打开时才关闭它
    if (openMenuMobile) {
      handleMenuMobile();
    }
  };

  useEffect(() => {
    console.log('Active index changed to:', activeIndex); // 调试：监控 activeIndex 变化
    if (mainSwiper) {
      mainSwiper.slideTo(activeIndex); // 强制同步内容切换
    }
  }, [activeIndex, mainSwiper]);

  const tabsData = [
    {
      title: '订单管理',
      cards: [
        { icon: 'ri-shopping-cart-line', title: '订单同步', description: '多渠道实时同步订单数据，避免遗漏' },
        { icon: 'ri-time-line', title: '实时跟踪', description: '监控订单状态，及时处理问题' },
        { icon: 'ri-refund-line', title: '自动退款', description: '智能处理退款请求，减少手动操作' },
        { icon: 'ri-file-list-line', title: '订单报表', description: '生成详细订单报告，支持导出' },
      ],
    },
    {
      title: '商品管理',
      cards: [
        { icon: 'ri-box-line', title: '商品上架', description: '快速上传并发布商品信息' },
        { icon: 'ri-edit-line', title: '批量编辑', description: '支持多商品同时修改属性' },
        { icon: 'ri-layout-line', title: '分类管理', description: '自定义商品分类和标签' },
        { icon: 'ri-delete-bin-line', title: '下架处理', description: '安全下架商品，避免库存问题' },
      ],
    },
    {
      title: '库存管理',
      cards: [
        { icon: 'ri-stack-line', title: '库存监控', description: '实时查看库存变化' },
        { icon: 'ri-home-4-line', title: '多仓库支持', description: '管理多个仓库库存' },
        { icon: 'ri-alert-line', title: '预警提醒', description: '库存不足时自动通知' },
        { icon: 'ri-sync-line', title: '库存同步', description: '与供应商系统联动' },
      ],
    },
    {
      title: '营销管理',
      cards: [
        { icon: 'ri-coupon-line', title: '优惠活动', description: '创建优惠券和促销规则' },
        { icon: 'ri-megaphone-line', title: '广告投放', description: '集成广告平台管理' },
        { icon: 'ri-search-line', title: 'SEO 优化', description: '提升搜索引擎排名' },
        { icon: 'ri-mail-send-line', title: '邮件营销', description: '发送个性化营销邮件' },
      ],
    },
    {
      title: '数据分析',
      cards: [
        { icon: 'ri-bar-chart-line', title: '销售统计', description: '分析销售趋势和数据' },
        { icon: 'ri-user-line', title: '行为分析', description: '跟踪用户行为路径' },
        { icon: 'ri-file-chart-line', title: '报表生成', description: '自定义数据报表' },
        { icon: 'ri-line-chart-line', title: '预测分析', description: '基于数据预测未来趋势' },
      ],
    },
    {
      title: '客户服务',
      cards: [
        { icon: 'ri-chat-3-line', title: '在线客服', description: '实时聊天支持客户咨询' },
        { icon: 'ri-feedback-line', title: '评价管理', description: '处理用户反馈和评论' },
        { icon: 'ri-service-line', title: '售后支持', description: '跟踪售后流程' },
        { icon: 'ri-heart-line', title: '忠诚度管理', description: '奖励忠实客户' },
      ],
    },
  ];



  return (
    <section className="bg-white md:py-[120px] pt-[60px]">
      <div className="max-w-[1920px] mx-auto md:px-[160px] px-[24px]">
        {/* 修改标题：左侧标题，右侧两个按钮，水平对齐 */}
        <div className="flex justify-between max-md:flex-col max-md:items-start md:items-center mb-16">
          <h2 className="md:text-[40px] text-2xl font-bold text-[#222222]">PinShop产品功能</h2>
          <div className="flex gap-4 max-md:mt-6">
            <button className="px-6 md:h-[46px] h-[42px] c-flex border border-[#303030] text-[#303030] rounded hover:bg-[#222222]/80 hover:text-white transition-colors" onClick={handleRegisterClick}>
              立即注册试用
            </button>
            <Link href="/contact-us" className="px-6 -[46px] c-flex border border-[#303030] text-[#303030] rounded hover:bg-[#222222]/80 hover:text-white transition-colors">
              咨询更多
            </Link>
          </div>
        </div>

        {/* PC端标签页 - 保持不变 */}
        <div className="hidden md:flex mb-8 border-b border-[rgba(34,34,34,0.15)]">
          {tabsData.map((tab, index) => (
            <div
              key={index}
              className={`relative text-left cursor-pointer px-6 py-2 text-lg font-bold text-gray-600 transition-colors duration-300 
                after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-[2px] after:bg-red-500 after:transition-all after:duration-300 after:z-10
                ${activeIndex === index ? 'after:w-full' : ''}`}
              onClick={() => setActiveIndex(index)}
            >
              {tab.title}
            </div>
          ))}
        </div>

        {/* 移动端标签页 - 可滑动 */}
        <div className="md:hidden mb-8">
          <Swiper
            slidesPerView={3.5} // 显示部分标签，提示可滑动
            spaceBetween={10}
            freeMode={true}
            className="tabs-swiper"
          >
            {tabsData.map((tab, index) => (
              <SwiperSlide key={index} className="!w-auto">
                <div
                  className={`relative text-left cursor-pointer px-4 py-2 text-base font-bold text-gray-600 transition-colors duration-300 
                    after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-[2px] after:bg-red-500 after:transition-all after:duration-300 after:z-10
                    ${activeIndex === index ? 'after:w-full' : ''}`}
                  onClick={() => setActiveIndex(index)}
                >
                  {tab.title}
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>

        {/* 内容 Swiper (主内容，左右切换) */}
        <Swiper
          spaceBetween={30}
          slidesPerView={1}
          onSwiper={setMainSwiper}
          onSlideChange={(swiper) => setActiveIndex(swiper.activeIndex)}
          modules={[FreeMode]}
          className="content-swiper"
        >
          {tabsData.map((tab, index) => (
            <SwiperSlide key={index} className='py-10'>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:flex md:justify-between gap-6">
                {tab.cards.map((card, cardIndex) => (
                  <div key={cardIndex} className="flex flex-col items-start p-4 md:p-6 rounded-lg border border-[#E5E5E5] flex-1">
                    <i className={`${card.icon} text-3xl md:text-4xl text-blue-500 mb-2`}></i>
                    <h3 className="text-lg md:text-xl font-bold mb-2">{card.title}</h3>
                    <p className="text-gray-600 text-sm md:text-base text-left">{card.description}</p>
                  </div>
                ))}
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>

      {/* 登录模态框 */}
      <LoginModal
        openModal={openLoginModal}
        setOpenModal={setOpenLoginModal}
        activeId={activeId}
        setActiveId={setActiveId}
      />
    </section>
  );
}
