import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';

// 定义数据数组（示例数据，根据实际需求调整）
const ventureData = [
    {
        round: '天使轮',
        capital: '天使投资',
        year: 2017,
        height: 330, // 使用数字，单位px
    },
    {
        round: 'A轮',
        capital: 'XX资本',
        year: 2018,
        height: 380,
    },
    {
        round: 'B轮',
        capital: 'YY资本',
        year: 2019,
        height: 430,
    },
    {
        round: 'C轮',
        capital: 'ZZ资本',
        year: 2020,
        height: 480,
    },
    {
        round: 'D轮',
        capital: 'AA资本',
        year: 2022,
        height: 530, // 最高
    },
    // 可以添加更多数据，如果超过5个，Swiper会自动分页
];

const VentureCapital: React.FC = () => {
    return (
        <section className='md:py-[120px] pt-[60px]'>
            <div className="max-w-[1920px] mx-auto md:px-[160px] px-[24px]">
                {/* 标题部分 */}
                <h2 className="md:text-[40px] text-2xl text-[#222222] font-bold  mb-12">被资本认可的历程</h2>

                {/* Swiper 部分 */}
                <div>
                    <Swiper
                        slidesPerView={2} // 移动端默认显示2个
                        spaceBetween={10} // 移动端间距更小
                        breakpoints={{
                            640: { // PC端断点
                                slidesPerView: 5,
                                spaceBetween: 20
                            }
                        }}
                        pagination={{ clickable: true }}
                        className="mySwiper"
                    >
                        {ventureData.map((item, index) => (
                            <SwiperSlide key={index}>
                                {/* 底部对齐容器 */}
                                <div className="flex flex-col h-full">
                                    {/* 柱子容器 - 使用 flex-grow 填充空间 */}
                                    <div className="flex-grow flex flex-col justify-end">
                                        {/* 轮次标签 - 移动到柱子内部顶部 */}
                                        <div className="mb-2 text-2xl text-[#222222] font-semibold ">{item.round}</div>
                                        
                                        {/* 柱子本身 - 移动端使用固定高度 */}
                                        <div
                                              className="
                                              w-full md:px-6 px-4
                                              pt-[40px]
                                              md:pb-[100px] pb-[50px]
                                              border-t-[3px] border-[#ff0000]
                                              flex flex-col justify-between 
                                              h-[300px] md:h-[var(--height)]
                                              bg-[linear-gradient(180deg,#F8F4EC_0%,#FFF_100%)]
                                            "
                                            style={{
                                              '--height': `${item.height}px`,
                                            } as React.CSSProperties}
                                          
                                        >
                                            <div className="text-[16px] text-[#222222] font-bold mt-2">{item.capital}</div>
                                            <div className="text-black/5 text-[44px] font-bold mb-2">{item.year}</div>
                                        </div>
                                    </div>
                                </div>
                            </SwiperSlide>
                        ))}
                    </Swiper>
                </div>
            </div>
        </section>
    );
};
export default VentureCapital;