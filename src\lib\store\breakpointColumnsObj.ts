import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

interface BreakpointColumns {
  default: number;
  1100: number;
  768: number;
}

interface State {
  col:number;
  setcol: (col: number) => void;
  breakpointColumns: BreakpointColumns;
  changeBreakpointColumns: (col: number) => void;
}

export const useBreakpointColumns = create(
  devtools(
    persist<State>(
      (set) => ({
        col:3,
        setcol:(col:number) => set((state) => ({ col})),
        breakpointColumns: {
          default: 3,
          1100: 3,
          768: 2,
        },
        changeBreakpointColumns: (col: number) => 
          set((state) => ({
            breakpointColumns: {
              default: col,
              1100: col,
              768:col, // 
            },
          })),
      }),
      {
        name: "breakpoint-columns-storage",
        skipHydration: true 
      }
    ),
    {
      name: "BreakpointColumnsStore",
    }
  )
);