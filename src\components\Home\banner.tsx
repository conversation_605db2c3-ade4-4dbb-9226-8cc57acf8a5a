"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation, Pagination } from "swiper/modules";

import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { Link } from "@/navigation";
import { observeElementIntersection } from "@/lib/utils/util";
import React from "react";
import Indicator from "../Indicator";
import { containerVariants, itemVariants } from "@/lib/utils/util";
import { motion } from "framer-motion";

const spaceBetween = 0;
const slidesPerView = 1;

type CardType = {
	title: string;
	title2?: string;
	title3?: string;
	desc: string;
	desc2?: string;
	desc3?: string;
	link: string;
	style?: React.CSSProperties; // 用于传递内联样式
};

const Card = ({
	title,
	title2,
	title3,
	link,
	desc,

	style = {}, // 默认样式为空
}: CardType) => {
	const t = useTranslations("banner");

	return (
		<div className={`absolute inset-0 h-full w-full bg-transparent max-md:relative`} style={style}>
			<div className="c-flex container h-full w-full max-md:hidden  max-md:px-0">
				<motion.div
					initial="hidden"
					whileInView="visible"
					viewport={{ once: true, amount: 0.5 }}
					variants={containerVariants}
					className="flex  w-full "
				>
					{/* font-serif */}
					<div className=" animate  animate__animated  animate__slideInUp banner-box relative  max-lg:top-0 max-md:w-full   max-md:py-[25px]  max-md:shadow-none">
						<motion.h2
							variants={itemVariants}
							className="max-md:text-md index_banner_swi mb-[120px] text-[200px] font-bold uppercase text-white max-lg:mb-10 max-lg:text-[60px] max-md:mb-1  "
							style={{ lineHeight: "1" }}
						>
							<span>{t(title)}</span>
							<br />
							<span>{t(title2)}</span>
						</motion.h2>

						<motion.h2
							variants={itemVariants}
							className=" bannerBtn mb-[3rem]  max-w-[380px] py-1.5   text-[20px] text-white hover:text-main max-md:text-sm max-md:font-bold   "
						>
							{t(desc)}
						</motion.h2>
						<Indicator
							isHovercolor={true}
							link="/products"
							color="#fff"
							className="border-white text-[14px]  text-white"
							cls=""
							msg={t("Shop now")}
						/>

						{/* <Link
              href={link}
              className="bannerBtn text-[24px] mt-8 block px-4 max-md:text-sm py-2 bg-[#fd0100] text-white w-fit    duration-300 animate animate__animated animate__delay-1500"
            >
              {t("Learn more")}
            </Link> */}
					</div>
				</motion.div>
			</div>
		</div>
	);
};

const Banner1 = () => {
	const cardData = {
		title: "Frank",
		title2: "lin",
		desc: "Selkirk Vanguard",
		link: "/products",
	};
	return (
		<div className="b-flex relative h-full w-full bg-white max-md:!h-auto max-md:flex-col">
			<img src={"/image/img/47.jpg"} className="h-full  w-full object-cover max-md:!h-auto" alt="BG.webp" />
			<Card {...cardData} />
		</div>
	);
};

function HomeBanner() {
	return (
		<div className="banner relative ">
			<Swiper
				modules={[Navigation, Autoplay, Pagination]}
				className=" home-banner max-h-[1500px]  overflow-hidden  max-lg:!h-auto max-md:mb-5"
				spaceBetween={spaceBetween}
				slidesPerView={slidesPerView}
				navigation={true}
				loop={true}
				pagination={{
					clickable: true,
					bulletActiveClass: "swiper-pagination-bullet-active !bg-white",
					bulletClass: "swiper-pagination-bullet home-banner-bullet",
				}}
				autoplay={{
					delay: 5000,
					disableOnInteraction: true,
				}}
			>
				<SwiperSlide>
					<Banner1 />
				</SwiperSlide>
				<SwiperSlide>
					<Banner1 />
				</SwiperSlide>
			</Swiper>
		</div>
	);
}
export default React.memo(HomeBanner);
