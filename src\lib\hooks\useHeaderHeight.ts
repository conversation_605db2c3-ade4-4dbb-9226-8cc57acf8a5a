"use client";

import { useEffect, useState } from "react";

/**
 * 自定义 Hook 用于计算导航栏的动态高度
 * @returns 返回用于 style 属性的 padding-top 值，自动兼容 PC、移动端和通知栏状态
 */
export const useHeaderHeight = () => {
	const [paddingTop, setPaddingTop] = useState("118px"); // 默认值
	const [isBannerVisible, setIsBannerVisible] = useState(true);

	useEffect(() => {
		// 检测是否为移动端
		const isMobile = () => window.innerWidth < 768; // md 断点

		// 监听 banner 的显示/隐藏状态
		const handleBannerChange = (event: CustomEvent) => {
			setIsBannerVisible(event.detail.isVisible);
		};

		// 监听窗口大小变化
		const handleResize = () => {
			calculatePaddingTop();
		};

		// 添加事件监听器
		window.addEventListener('bannerVisibilityChange', handleBannerChange as EventListener);
		window.addEventListener('resize', handleResize);

		// 计算高度并生成用于 style 的 padding-top 值
		const calculatePaddingTop = () => {
			if (isMobile()) {
				// 移动端固定高度
				setPaddingTop("64px");
			} else {
				// PC 端动态高度
				const bannerHeight = isBannerVisible ? 48 : 0; // banner 高度
				const headerHeight = 70; // 主导航栏高度
				const totalHeight = bannerHeight + headerHeight;
				setPaddingTop(`${totalHeight}px`);
			}
		};

		calculatePaddingTop();

		return () => {
			window.removeEventListener('bannerVisibilityChange', handleBannerChange as EventListener);
			window.removeEventListener('resize', handleResize);
		};
	}, [isBannerVisible]);

	return paddingTop;
};