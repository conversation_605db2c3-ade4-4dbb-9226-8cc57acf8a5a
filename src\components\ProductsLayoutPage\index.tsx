"use client";
import React, { useState } from 'react';
import { HomeTaile } from "@/components/Contact/ConcatPage";
import { useTranslations } from 'next-intl';
import { Drawer } from 'antd';
import useIsMobile from '@/lib/hooks/useIsMobile';
import Categories from "@/components/ProductList/Categories";
import { SearchBar } from "../SearchBar/page";
import { useBreakpointColumns } from '@/lib/store/breakpointColumnsObj';
function Index({children,channel,locale,categoriesData}) {
  const t = useTranslations();
    const [open, setOpen] = useState(false);
    const showDrawer = () => {
      setOpen(true);
    };
  
    const onClose = () => {
      setOpen(false);
    };

    let isMobile= useIsMobile()

 
 let {changeBreakpointColumns,col,setcol} = useBreakpointColumns()
    
    function changeBreakpointColumnsObj(col:number){
      changeBreakpointColumns(col)
      setcol(col)
    }
  return (
    <>
        <HomeTaile  msg={t('common.Product')}/>

<section className="blog list py-10 md:py-20">
  <div className="container">
          <div className="flex items-center justify-between py-6">
            <div onClick={showDrawer} className="flex cursor-pointer items-center gap-x-3">
              <GETsvg /> <span className="">{t('common.Categories')}</span>
            </div>
            <div className="flex cursor-pointer items-center gap-x-3">
              <div onClick={() => changeBreakpointColumnsObj(2)} >
                <GETsvg2 color={col==2?'#000':"#a1a1a1"} />
              </div>
              <div onClick={() => changeBreakpointColumnsObj(3)}>
                <GETsvg3 color={col==3?'#000':"#a1a1a1"} />
              </div>{" "}
              <div className="max-md:hidden" onClick={() => changeBreakpointColumnsObj(4)}>
                <GETsvg4 color={col==4?'#000':"#a1a1a1"} />
              </div>
            </div>
            <div className="max-md:hidden"></div>
          </div>
    <div className="flex justify-between gap-y-12 max-xl:flex-col">
      {/* <div className="right pr-5 xl:w-1/4">
        <SearchBar channel={channel} />
        <Categories locale={locale} categoriesData={categories} />
      </div> */}
      <div className="flex flex-1 flex-col">{children}</div>
    </div>
  </div>
</section>
<Drawer
  title={t('common.Categories')}
  placement={"left"}
  onClose={onClose}
  open={open}
  key={"left"}
  width={isMobile?"86vw":'30vw'}
>
        <SearchBar channel={channel} />
        <Categories locale={locale} categoriesData={categoriesData} />
</Drawer>
      
    </>
  );
}

export default React.memo(Index);

function GETsvg() {
	return (
		<svg width="20" height="12" viewBox="0 0 20 12" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M0 1C0 0.734784 0.105357 0.48043 0.292893 0.292893C0.48043 0.105357 0.734784 0 1 0H19C19.2652 0 19.5196 0.105357 19.7071 0.292893C19.8946 0.48043 20 0.734784 20 1C20 1.26522 19.8946 1.51957 19.7071 1.70711C19.5196 1.89464 19.2652 2 19 2H1C0.734784 2 0.48043 1.89464 0.292893 1.70711C0.105357 1.51957 0 1.26522 0 1ZM3 6C3 5.73478 3.10536 5.48043 3.29289 5.29289C3.48043 5.10536 3.73478 5 4 5H16C16.2652 5 16.5196 5.10536 16.7071 5.29289C16.8946 5.48043 17 5.73478 17 6C17 6.26522 16.8946 6.51957 16.7071 6.70711C16.5196 6.89464 16.2652 7 16 7H4C3.73478 7 3.48043 6.89464 3.29289 6.70711C3.10536 6.51957 3 6.26522 3 6ZM8 10C7.73478 10 7.48043 10.1054 7.29289 10.2929C7.10536 10.4804 7 10.7348 7 11C7 11.2652 7.10536 11.5196 7.29289 11.7071C7.48043 11.8946 7.73478 12 8 12H12C12.2652 12 12.5196 11.8946 12.7071 11.7071C12.8946 11.5196 13 11.2652 13 11C13 10.7348 12.8946 10.4804 12.7071 10.2929C12.5196 10.1054 12.2652 10 12 10H8Z"
				fill="currentColor"
			></path>
		</svg>
	);
}

function GETsvg2({ color = "#a1a1a1" }: { color?: string }) {
	return (
		<svg width="13" height="13" viewBox="0 0 13 13" fill="none">
			<circle cx="2.4375" cy="2.4375" r="2.4375" fill={color}></circle>
			<circle cx="10.5625" cy="2.4375" r="2.4375" fill={color}></circle>
			<circle cx="2.4375" cy="10.5625" r="2.4375" fill={color}></circle>
			<circle cx="10.5625" cy="10.5625" r="2.4375" fill={color}></circle>
		</svg>
	);
}

function GETsvg3({ color = "#a1a1a1" }: { color?: string }) {
	return (
		<svg width="22" height="13" viewBox="0 0 22 13" fill="none">
			<circle cx="2.4375" cy="2.4375" r="2.4375" fill={color}></circle>
			<circle cx="10.5625" cy="2.4375" r="2.4375" fill={color}></circle>
			<circle cx="18.6875" cy="2.4375" r="2.4375" fill={color}></circle>
			<circle cx="2.4375" cy="10.5625" r="2.4375" fill={color}></circle>
			<circle cx="10.5625" cy="10.5625" r="2.4375" fill={color}></circle>
			<circle cx="18.6875" cy="10.5625" r="2.4375" fill={color}></circle>
		</svg>
	);
}

function GETsvg4({ color = "#a1a1a1" }: { color?: string }) {
	return (
		<svg width="30" height="13" viewBox="0 0 30 13" fill="none">
			<circle cx="2.4375" cy="2.4375" r="2.4375" fill={color}></circle>
			<circle cx="10.5625" cy="2.4375" r="2.4375" fill={color}></circle>
			<circle cx="18.6875" cy="2.4375" r="2.4375" fill={color}></circle>
			<circle cx="26.8125" cy="2.4375" r="2.4375" fill={color}></circle>
			<circle cx="2.4375" cy="10.5625" r="2.4375" fill={color}></circle>
			<circle cx="10.5625" cy="10.5625" r="2.4375" fill={color}></circle>
			<circle cx="18.6875" cy="10.5625" r="2.4375" fill={color}></circle>
			<circle cx="26.8125" cy="10.5625" r="2.4375" fill={color}></circle>
		</svg>
	);
}
