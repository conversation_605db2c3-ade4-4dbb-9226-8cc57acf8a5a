"use client";
import React, { useState } from "react";
import LoginModal from "@/components/User/login-modal";
import { useTranslations } from "next-intl";
import { ConsultationPopover } from "@/components/ZC/BannerStatistics";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { Link } from "@/navigation";
interface BannerProps {
	backgroundImage?: string;
	subtitle?: string;
	mainTitle?: string;
}

const CommonFooter: React.FC<BannerProps> = ({
	backgroundImage = "/image/solution/footer-banner.webp",
	subtitle = "现在就用 Pinshop 启动你的品牌网站	",
	mainTitle = "启动一个高性能、可拓展、可增长的智能网站，从 Pinshop 开始。",
}) => {
	// 登录模态框状态管理
	const [openLoginModal, setOpenLoginModal] = useState(false);
	const [activeId, setActiveId] = useState(1); // 1: 登录, 2: 注册
	// 处理注册按钮点击
	const handleRegisterClick = () => {
		setActiveId(2); // 设置为注册面板
		setOpenLoginModal(true);
	};
	return (
		<>
			<div className="relative overflow-hidden">
				{/* 背景图片 */}
				<div
					className="w-full bg-cover bg-center bg-no-repeat"
					style={{
						backgroundImage: `url(${backgroundImage})`,
						paddingTop: "40%", // 设置宽高比，可以根据实际图片调整
					}}
				></div>

				{/* 内容 */}
				<div className="absolute inset-0 flex items-center justify-center">
					<div className="container relative z-10">
						<div className="text-center">
							{/* 副标题 */}
							<p className="mb-6 text-lg text-titleLight max-md:mb-4 max-md:text-base">{subtitle}</p>

							{/* 主标题 */}
							<h2 className="mb-8 max-md:mb-4 text-4xl font-bold leading-tight text-titleLight xl:max-w-[600px] mx-auto  max-md:text-xl">
								{mainTitle}
							</h2>
							<div className="flex justify-center">
								<Link
									href="/pricing"
									// onClick={handleRegisterClick}
									className={`rounded bg-black px-[24px] py-[11px] mr-4 text-[16px] font-medium text-white hover:bg-opacity-80`}
								>
									立即建站
								</Link>
								{/* 按钮 */}
								<ConsultationPopover />
							</div>
						</div>
					</div>
				</div>
			</div>
			{/* 登录模态框 */}
			<LoginModal
				openModal={openLoginModal}
				setOpenModal={setOpenLoginModal}
				activeId={activeId}
				setActiveId={setActiveId}
			/>
		</>
	);
};

export default CommonFooter;
