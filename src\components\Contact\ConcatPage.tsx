
"use client"
import { contactInfo, contactObj } from '@/lib/contacts';
import { useTranslations } from 'next-intl';
import React from 'react'
import ContactForm from '../InquryForm/ContactForm';
import clsx from 'clsx';
import { Link } from '@/navigation';
import { useHeaderHeight } from "@/lib/hooks/useHeaderHeight";

export default function ConcatPage({locale}:{ locale: string }) {
  const t = useTranslations('nav');
  const paddingTop = useHeaderHeight();

  const navigation = {
    contact: [
      { title:t('Address'),value: `${contactInfo.address}`},
      { title:t('Phone'),value: `${contactInfo.phone}`},
      { title:t('Email'),value: `${contactInfo.email}`},
    ],
    };

	const items = [
		{ icon: "ri-facebook-fill", text: "Facebook", link: contactObj.FaceBook },
		{ icon: "ri-instagram-line", text: "Instagram", link: contactObj.instagram },
		{ icon: "ri-youtube-fill", text: "Youtube", link: contactObj.Youtube },
		{ icon: "ri-pinterest-fill", text: "Pinterest", link: contactObj.Pinterest },
		{ icon: "ri-linkedin-fill", text: "LinkedIn", link: contactObj.Linkedin },
	];
  return (
    <div className="bg-[#F2F4F5] " style={{ paddingTop }}>
      <HomeTaile  msg={t("Contact Us")}/>
      <div className=''>
        <iframe
          src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d234293.40709290045!2d112.90403468400288!3d23.43034540703068!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3402e7b8329647ad%3A0x99fb79bdc9d67e34!2sHuadu%20District%2C%20Guangzhou%2C%20Guangdong%20Province%2C%20China!5e0!3m2!1sen!2sjp!4v1733028016280!5m2!1sen!2sjp"
          width="100%"
          height="650"
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
        ></iframe>
      </div>

      <div className='w-full bg-[#f3f4f5] py-20 max-md:py-0'>
      <div className="bg-white mx-auto grid max-w-7xl grid-cols-1 lg:grid-cols-2 py-20  max-md:py-8 text-[28px]  ">
      <div className="px-6 max-lg:mb-10">

        <h3 className="text-black font-medium text-[28px]">{t('Visit Our Store')}</h3>
        <div className="mt-10 md:mt-0">
          <ul role="list" className="mt-6 space-y-4 text-black flex flex-col">
            {navigation.contact.map((item, index) => (
              <li key={index} className='text-[14px] mb-[15px]'>
                <div className='mb-[15px]'>{item.title}</div>
                <div className='text-[#545454]'>{item.value}</div>
              </li>
            ))}
          <li  className='text-[14px] mb-[15px]'>
                <div className='mb-[15px]'>Open Time</div>
                <div className='text-[#545454] mb-[15px]'>{t('Our store')}</div>
                <div className='text-[#545454] mb-[15px]'>{t('exchange')}</div>
              </li>
              <li className='flex gap-x-2'>
 									{items.map((item, index) => (
										<Link key={index} href={item.link} target="_blank" className="text-black hover:text-mian">
											<i key={item.text} className={clsx(item.icon, "text-xl")}></i>
										</Link>
									))}
              </li>
          </ul>

        </div>
      </div>
      <div className="container bg-white  ">
      <h3 className="text-black font-medium text-[28px] mb-[20px]">{t('Get in Touch')}</h3>
      <ContactForm locale={locale} title={t('getInstantQuoteAndTS1')}
        // @ts-ignore
                     titleContent={t("fillOutIn")} className="!py-0 " innerClassName="!shadow-none !rounded-none"></ContactForm>
    </div>

    </div>
      </div>



  </div>
  )
}

export function HomeTaile({msg}){
  return (
    <div className="py-[80px] bg-[url(/image/img/ct/11.png)]   bg-no-repeat  bg-[length:100%_100%]">
      <div className="container max-md:mt-5">
        <h2 className="text-center font-medium text-bloak text-[42px]  max-md:text-2xl ">{msg}</h2>
      </div>
    </div>
  )
}
