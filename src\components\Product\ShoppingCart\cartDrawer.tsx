"use client";
import { ReactNode, useEffect, useState } from "react";
import { useTranslations } from "next-intl";

import { useUserStore } from "@/lib/store/user";
import CartContent from "./cartContent";
import { useRouter } from "@/navigation";
import React from "react";
import { CartDrawerOpen } from "@/lib/store/CartDrawerOpen";
import { Drawer } from "antd";

// 新增端类型判断hook，合并手机和平板逻辑
const useDeviceType = () => {
	const [deviceType, setDeviceType] = useState<"mobile" | "tablet" | "pc">("pc");
	useEffect(() => {
		const handleResize = () => {
			if (window.innerWidth <= 768) {
				setDeviceType("mobile");
			} else if (window.innerWidth > 768 && window.innerWidth <= 1024) {
				setDeviceType("tablet");
			} else {
				setDeviceType("pc");
			}
		};
		handleResize();
		window.addEventListener("resize", handleResize);
		return () => {
			window.removeEventListener("resize", handleResize);
		};
	}, []);
	return deviceType;
};

type Props = {
	children: ReactNode;
	callback?: (...arg: any) => void;
	className?: string;
};

const CartDrawer = (props: Props) => {
	// const [open, setOpen] = useState(true);
	let { open, setOpen } = CartDrawerOpen();

	const { userInfo } = useUserStore();
	const t = useTranslations();
	const router = useRouter();
	const handlerOpen = () => {
		// if (!userInfo) {
		//   const login = document.querySelector("#web-login") as HTMLLIElement;
		//   login.click();
		//   return;
		// }
		props?.callback?.(true);
		setOpen(true);
	};
	useEffect(() => {
		setOpen(false);
	}, [router]);

	let deviceType = useDeviceType();

	return (
		<>
			<div onClick={handlerOpen} className={props?.className ? props.className : ""}>
				{props.children}
			</div>
			<Drawer // 移除默认关闭按钮
				closeIcon={null}
				width={deviceType === "mobile" ? "86vw" : deviceType === "tablet" ? "45vw" : "30vw"}
				className=" cursor-auto overflow-hidden "
				open={open}
				placement={"right"}
				onClose={() => setOpen(false)} // 自定义标题，将关闭按钮移到右侧
				title={
					<div className="flex w-full items-center justify-between">
						<span>{t("shop.0b1dcf5c5c3d9b4fe53b771d965840f7159e")}</span>

						<i
							onClick={() => setOpen(false)}
							className="ri-close-line cursor-pointer text-3xl text-black"
						></i>
					</div>
				}
			>
				<div className="">
					{/* @ts-ignore */}
					<CartContent layout="col" />
				</div>
			</Drawer>
		</>
	);
};

export default React.memo(CartDrawer);
