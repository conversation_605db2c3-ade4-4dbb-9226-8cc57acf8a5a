"use client";
import { useTranslations } from "next-intl";
import { Link } from "@/navigation";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { ConsultationPopover } from "@/components/ZC/BannerStatistics";
import { motion } from "framer-motion";
export default function HomeOutSea() {
	const Arr = [
		{
			id: 1,
			title: "技术优势",
			desc: "采用 Next.js 与 React 技术构建，支持服务端渲染、静态生成、一键部署至 Vercel，内置 SEO 与性能优化机制。",
		},
		{
			id: 2,
			title: "适用场景",
			desc: "面向初创企业、SaaS 团队、独立品牌与出海商家，覆盖官网、电商、展示站等多种建站需求，支持多语种出海站点。",
		},
		{
			id: 3,
			title: "AI 智能能力",
			desc: "可选配 AI 内容能力，从自动 SEO 到多语种视频，自动生成标题描述、智能用户交互、数据分析优化，助你轻松增长。",
		},
		{
			id: 4,
			title: "模板展示",
			desc: "挑选经过行业设计优化的模板，移动端适配、SEO 结构完善，支持个性化定制、快速部署，快速发布国际化站点。",
		},
		{
			id: 5,
			title: "一站式服务",
			desc: "从技术架构到内容生成，从模板选择到部署上线，提供完整的跨境电商解决方案，让您的品牌轻松出海。",
		},
	];
	return (
		<>
			<section className="max-md:py-40px bg-[#5959FF] py-[80px]">
				<div className="container flex gap-20 max-md:flex-col max-md:gap-10">
					{/* 左侧内容 */}
					<div className="flex flex-1 flex-col">
						<div className="max-w-md max-md:max-w-full">
							<p className="text-[16px] text-gray-300">品店 PinShop </p>
							<h2 className="my-[24px] text-[40px] font-bold leading-tight text-white max-md:text-[20px] ">
								品牌出海解决方案
							</h2>
							<p className="text-[16px] text-white">无论您是何种类型的卖家，我们都可以提供对应的解决方案</p>
							<div className="mt-[46px] max-md:mt-[23px]">
								<Link
									href={`/solution`}
									className={`mr-4 inline-block rounded bg-white px-[24px] py-[11px] text-[16px] font-medium text-[#5959FF] hover:bg-opacity-80  hover:text-[#5959FF]`}
								>
									方案咨询
								</Link>
								<ConsultationPopover theme="white" />
							</div>
						</div>
					</div>
					{/* 右侧FAQ列表 */}
					<div className="flex-1">
						{Arr.map((item: any, index: number) => {
							return (
								<div key={index}>
									<Link
										href="javascript:void(0)"
										className="group  block border-b border-white/50 duration-300 hover:border-white"
									>
										<div className="flex gap-6 pb-10 pt-6 max-md:py-4">
											<span className="text-2xl font-medium text-white max-md:text-base">0{item.id}</span>
											<div className="pl-10 flex-1">
												<div className=" flex justify-between">
													<span className="text-2xl font-medium text-white max-md:text-base relative">
                            {item.title}
	                          <span className="absolute bottom-0 left-0 h-[1px] w-full origin-left scale-x-0 bg-white transition-all duration-300 group-hover:scale-x-100"></span>
                          </span>
													<svg
														className="ml-2 h-4 w-4 flex-shrink-0 text-white transition-transform duration-300 group-hover:translate-x-1"
														fill="none"
														stroke="currentColor"
														viewBox="0 0 24 24"
													>
														<path
															strokeLinecap="round"
															strokeLinejoin="round"
															strokeWidth={2}
															d="M9 5l7 7-7 7"
														/>
													</svg>
												</div>
												<p className="mt-[16px] text-base font-medium text-white text-white/70">
													{item.desc}
												</p>
											</div>
										</div>
									</Link>
								</div>
							);
						})}
					</div>
				</div>
			</section>
		</>
	);
}
