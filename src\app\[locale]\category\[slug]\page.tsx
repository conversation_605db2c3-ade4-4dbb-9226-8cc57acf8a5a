// app/blog/page.tsx
import React from "react";
import * as Icon from "@phosphor-icons/react/dist/ssr";
import { getTranslations, unstable_setRequestLocale } from "next-intl/server";
import { getBlogCls, getBlogTags } from "@/lib/api/blog";
import RecentPosts from "@/components/Blogs/RecentPosts";
import Categories from "@/components/Blogs/Categories";
import TagsCloud from "@/components/Blogs/Tags";
// import HandlePagination from '@/components/Other/HandlePagination';
import { type Blog } from "@/lib/@types/api/blog";
import { type MyPageProps } from "@/lib/@types/base";
import { fetchBlogList } from "@/app/[locale]/page";
import { Link } from "@/navigation";
import BlogSeach from "@/components/Blogs/BlogSeach";
// import { Pagination } from 'antd';
import { generateSeo } from "@/lib/utils/seo.ts";
import type { Metadata } from "next";
import { getBasePageSeo } from "@/lib/api/seo";
import { HomeTaile } from "@/components/Contact/ConcatPage";
import BlogContent from "@/components/Blogs/BlogContent";

export const generateMetadata = async (props: MyPageProps): Promise<Metadata> => {
	props.params.page = ["category"];
	// 获取基础页面的SEO数据
	const seo = await getBasePageSeo(props);
	// 生成最终的SEO信息，并返回
	return generateSeo(props, {
		...seo,
		ogType: "website",
	});
};
// 使用async函数获取数据并渲染
export default async function BlogCategory({ params, searchParams }: MyPageProps) {
  unstable_setRequestLocale(params.locale); 
	// const t =await getTranslations();
	const currentPage = 1;
	const pageSize = 6; // 每页显示6条数据

	let { blogList, blogCount } = await fetchBlogList({
		lang_code: { lang_code: params.locale },
		page: currentPage,
		limit: pageSize,
		cls_slug_in: [{ slug: params.slug }],
	});

	// if (blogCount) {
	// 	pageCount = Math.ceil(blogCount / limit);
	// }
	//
	const clsList = await getBlogCls({ lang_code: { lang_code: params.locale } });
	const tagList = await getBlogTags({ lang_code: { lang_code: params.locale } });

	//
	// const handlePageChange = async (selected: number) => {
	// 	// 页码更改处理逻辑
	// 	console.log("Selected page:", selected);
	// };
	const t = await getTranslations();
	return (
		<>
			<HomeTaile msg={t("common.Blog")} />
			<section className="blog list py-10 md:py-20">
				<div className="container">
					<div className="flex justify-between gap-y-12 max-xl:flex-col">
						<div className="right xl:w-1/4 xl:pr-[52px]">
							{/* <BlogSeach></BlogSeach> */}

							<Categories clsList={clsList} />
							<TagsCloud tagList={tagList} />
							{blogList.length > 0 && <RecentPosts blogList={blogList} />}
						</div>
						<div className="left xl:w-3/4 xl:pr-2 ">
							<BlogContent
								params={params}
								blogLists={blogList}
								locale={params.locale}
								search={searchParams?.search || ""}
								currentPage={currentPage}
								total={blogCount}
								pageSize={pageSize}
							/>
						</div>
					</div>
				</div>
			</section>
		</>
	);
}
