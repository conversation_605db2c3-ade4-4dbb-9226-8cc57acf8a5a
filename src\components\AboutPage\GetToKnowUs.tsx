'use client'
import React, { useState } from "react";
import { motion } from 'framer-motion';
import LoginModal from "../User/login-modal";
import useMenuMobile from "@/store/useMenuMobile";
import { Link } from "@/navigation";


const HeroSection = () => {
  const { openMenuMobile, handleMenuMobile } = useMenuMobile();
  const [activeId, setActiveId] = useState(1); // 1: 登录, 2: 注册
  const [openLoginModal, setOpenLoginModal] = useState(false);


  const variants = {
    hidden: { opacity: 0, y: 50 },
    visible: (index: number) => ({
      opacity: 1,
      y: 0,
      transition: { duration: 0.8, delay: index * 0.2 } // 时间差：每个元素延迟0.2s
    }),
  };

  const handleRegisterClick = () => {
    setActiveId(2); // 设置为注册面板
		setOpenLoginModal(true);
		// 只有在移动端菜单打开时才关闭它
		if (openMenuMobile) {
			handleMenuMobile();
		}
  };

  return (
    <section className="md:py-[120px] py-[60px] bg-[#f8f4ec]">
      <div className="container mx-auto px-4">
        <div className="text-center max-w-4xl mx-auto">
          <motion.h1
            custom={0} // 第一个元素，delay=0
            variants={variants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="text-4xl md:text-5xl font-bold text-[#222222] mb-6"
          >
            一站式跨境电商出海解决方案
          </motion.h1>
          <motion.p
            custom={1} // 第二个元素，delay=0.2s
            variants={variants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="text-lg text-gray-600 mb-8"
          >
            跨境全链路SaaS系统，抢夺跨境生态体系，打通跨境业务闭环，助力商家快速出海。
          </motion.p>

          <motion.div
            custom={2} // 第三个元素，delay=0.4s
            variants={variants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="flex justify-center space-x-4 mb-12"
          >
            <button className="bg-[#222222] text-white px-7 py-2.5 rounded hover:bg-[#222222]/80 transition-colors" onClick={handleRegisterClick}>
              免费试用
            </button>

            <Link href="/contact-us">
              <button className="border border-[#222222] text-[#222222] px-7 py-2.5 rounded hover:border-gray-400 transition-colors">
                开店咨询
              </button>
            </Link>
          </motion.div>
        </div>
      </div>
      {/* 注册/登录模态框 */}
      <LoginModal
        openModal={openLoginModal}
        setOpenModal={setOpenLoginModal}
        activeId={activeId}
        setActiveId={setActiveId}
      />
    </section>
  );
};

export default HeroSection;
