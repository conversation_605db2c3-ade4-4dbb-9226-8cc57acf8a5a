"use client"
import React, { useRef, useEffect } from 'react'
import { Swiper, SwiperSlide } from "swiper/react";
import { FreeMode } from "swiper/modules";
import "swiper/css";
import {Link} from '@/navigation';
import { useTranslations } from "next-intl";
import { motion } from "framer-motion";


function CompanyValues() {
  // const t = useTranslations("about");
  const values = [
    { title: "专注差异化", desc: "我们专注于构建差异化的商业价值，不断在商业模式、业务策略、产品设计、客户群体、垂直行业和特定场景中探索出独特的优势，提供具有差异化价值的解决方案，在市场竞争中脱颖而出。" },
    { title: "深入洞察业务", desc: "我们持续洞察我们所处的行业，全面深入地梳理客户的业务和场景，提供最专业和最实用的解决方案，超越客户的期望。" },
    { title: "打造卓越产品", desc: "我们坚持卓越和开放的原则，高效构建有竞争力的产品，创造最佳客户体验，赢得客户的认可。" },
    { title: "提供优质服务", desc: "我们秉承以客户为本的初心，精准把握客户的需求与困难，提供专业且高效的服务，助力客户的商业成功。" },
  ];
  const swiperRef = useRef<any>(null);

  const handleSwiperInit = (swiper: any) => {
    swiperRef.current = swiper;
    if (swiper.slides?.length > 0) {
      const slideWidth = swiper.slides[0].getBoundingClientRect().width || 0;
      const offset = -slideWidth / 2;
      swiper.wrapperEl.style.transitionDuration = '0ms';
      swiper.setTranslate(offset);
      swiper.update();
      setTimeout(() => {
        swiper.wrapperEl.style.transitionDuration = '';
      }, 10);
    }
  };

  return (
    <div className="bg-[#f8f4ec]">
      <div className="max-w-[1920px] mx-auto bg-[#f2eada] md:px-[160px] px-[24px]">
        <div className="md:py-[120px] py-[60px]">
          <h2 className="text-left text-3xl font-bold md:mb-16 mb-8">我们的价值观</h2>
          <Swiper
            onSwiper={handleSwiperInit}
            modules={[FreeMode]}
            freeMode={true}
            loop={false}
            slidesPerView={1} // 移动端默认显示1个
            spaceBetween={16} // 移动端间距更小
            breakpoints={{
              640: { // PC端断点
                slidesPerView: 2,
                spaceBetween: 36
              }
            }}
            className="select-none cursor-grab active:cursor-grabbing md:!overflow-visible"
          >
            {values.map((value, index) => (
              <SwiperSlide 
                key={index} 
                className="w-full sm:w-[782px] h-full p-6 sm:p-9 bg-white border border-black/10 rounded-lg flex flex-col justify-between overflow-hidden"
              >
                <h3 className="text-2xl font-semibold mb-4 sm:mb-9">{value.title}</h3>
                <p className="text-[#222222] text-lg mb-4">{value.desc}</p>

                <div className='mt-12 pt-6'>
                  <div className='text-[#222222] text-[14px]'>Pinshop</div>
                  <Link href="#" className="text-[#222222] hover:underline mt-12">Open To More</Link>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>
    </div>
  )
}

export default CompanyValues