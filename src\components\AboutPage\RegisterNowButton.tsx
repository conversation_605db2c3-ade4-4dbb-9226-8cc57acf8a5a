'use client'
import React, { useState } from "react";
import { Link } from '@/navigation';
import { motion } from 'framer-motion';
import LoginModal from "../User/login-modal";
import useMenuMobile from "@/store/useMenuMobile";


export default function RegisterNowButton() {
	const { openMenuMobile, handleMenuMobile } = useMenuMobile();
    const [openLoginModal, setOpenLoginModal] = useState(false);
	const [activeId, setActiveId] = useState(1); // 1: 登录, 2: 注册


    const handleRegisterClick = () => {
		setActiveId(2); // 设置为注册面板
		setOpenLoginModal(true);
		// 只有在移动端菜单打开时才关闭它
		if (openMenuMobile) {
			handleMenuMobile();
		}
	};
    return (
        <section className="relative bg-gradient-to-br from-[#F5F7FA] to-[#E8F0FE] py-16 md:py-[120px] 2xl:min-h-[1000px] overflow-hidden flex flex-col justify-center">
            {/* 大气背景效果 */}
            <div className="absolute inset-0 overflow-hidden">
                {/* 左侧波浪装饰 */}
                <div className="absolute left-0 top-0 w-[50%] h-full overflow-hidden">
                    <svg className="absolute left-0 top-0 w-full h-full" viewBox="0 0 500 1000" preserveAspectRatio="none">
                        <path d="M0,0 Q150,200 300,400 T500,800 L500,1000 L0,1000 Z" fill="rgba(255, 59, 48, 0.03)" />
                        <path d="M0,0 Q100,300 200,600 T400,1000 L500,1000 L0,1000 Z" fill="rgba(255, 59, 48, 0.02)" />
                    </svg>
                </div>
                
                {/* 右侧波浪装饰 */}
                <div className="absolute right-0 top-0 w-[50%] h-full overflow-hidden">
                    <svg className="absolute right-0 top-0 w-full h-full" viewBox="0 0 500 1000" preserveAspectRatio="none">
                        <path d="M500,0 Q350,200 200,400 T0,800 L0,1000 L500,1000 Z" fill="rgba(74, 144, 226, 0.03)" />
                        <path d="M500,0 Q400,300 300,600 T100,1000 L0,1000 L500,1000 Z" fill="rgba(74, 144, 226, 0.02)" />
                    </svg>
                </div>
                
                {/* 顶部装饰元素 */}
                <div className="absolute top-0 left-0 w-full h-20 bg-gradient-to-b from-[#ffffff80] to-transparent"></div>
                
                {/* 底部装饰元素 */}
                <div className="absolute bottom-0 left-0 w-full h-20 bg-gradient-to-t from-[#ffffff80] to-transparent"></div>
            </div>

            <motion.div
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="relative z-10 max-w-[920px] mx-auto px-4 md:px-[160px]"
            >
                <p className="mb-4 md:mb-5 text-base md:text-[18px] text-[#555555] text-center">
                    7天0元免费试用，开启独立站增长之旅！
                </p>
                <h2 className="text-2xl md:text-[40px] leading-normal md:leading-[1.3] font-bold text-[#222222] text-center">
                    即刻加入我们全球50W+卖家行列<br />
                    5步即可搭建营销型获客网店
                </h2>

                <div className="mt-8 md:mt-10 flex flex-col sm:flex-row items-center justify-center gap-4 md:gap-6">
                    <button
                        onClick={handleRegisterClick}
                        className="inline-flex items-center justify-center w-full sm:w-[200px] h-12 md:h-[56px] rounded-lg bg-gradient-to-r from-[#FF3B30] to-[#FF6B5F] text-white hover:text-white text-base md:text-[18px] font-semibold hover:shadow-xl hover:shadow-[#FF3B30]/20 transition-all duration-300 transform hover:-translate-y-1"
                    >
                        立即注册试用
                    </button>
                    <Link
                        href="/contact-us"
                        className="inline-flex items-center justify-center w-full sm:w-[200px] h-12 md:h-[56px] rounded-lg border border-[#222222] text-[#222222] text-base md:text-[18px] font-semibold hover:bg-[#222222] hover:text-white transition-colors"
                    >
                        开店咨询
                    </Link>
                </div>
            </motion.div>

            {/* 登录模态框 */}
            <LoginModal
                openModal={openLoginModal}
                setOpenModal={setOpenLoginModal}
                activeId={activeId}
                setActiveId={setActiveId}
            />
        </section>
    );
}    