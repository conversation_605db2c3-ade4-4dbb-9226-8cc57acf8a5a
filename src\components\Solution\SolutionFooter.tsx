'use client'
import { useModalInquiryContext } from "@/context/ModalInquiryContext";
import { useTranslations } from "next-intl";
import React from "react";

interface BannerProps {
  backgroundImage?: string;
  subtitle?: string;
  mainTitle?: string;
  buttonText?: string;
}

const SolutionFooter: React.FC<BannerProps> = ({
  backgroundImage = "/image/solution/footer-banner.webp",
  subtitle = "SolutionFootertitle",
  mainTitle = "SolutionFooterDesc",
  buttonText = "SolutionFooterButton",
}) => {
  const { openModalInquiry } = useModalInquiryContext();
  const t = useTranslations("solution");

  return (
    <div className="relative overflow-hidden">
      {/* 背景图片 */}
      <div 
        className="w-full bg-cover bg-center bg-no-repeat"
        style={{ 
          backgroundImage: `url(${backgroundImage})`,
          paddingTop: '40%' // 设置宽高比，可以根据实际图片调整
        }}
      >
      </div>

      {/* 内容 */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="container mx-auto relative z-10 px-4 max-md:px-4">
          <div className="text-center max-w-4xl mx-auto">
            {/* 副标题 */}
            <p className="text-lg max-md:text-base text-[#222222] mb-6 max-md:mb-4">
              {t(subtitle)}
            </p>
            
            {/* 主标题 */}
            <h2 className="text-4xl max-md:text-xl font-bold text-[#222222] mb-8 max-md:mb-6 leading-tight">
              {t(mainTitle)}
            </h2>
            
            {/* 按钮 */}
            <button
              onClick={()=>openModalInquiry(false)}
              className="px-8 max-md:px-6 py-4 max-md:py-3 bg-[#222222] text-white font-medium rounded-sm hover:bg-gray-700 transition-colors text-base max-md:text-sm"
            >
              {t(buttonText)}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SolutionFooter; 